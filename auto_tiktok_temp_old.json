{"name": "My workflow", "nodes": [{"parameters": {"content": "# TikTok Automation Pipeline\n\n## Workflow Overview:\n1. **OpenAI (GPT-4)** → Generate viral TikTok stories\n2. **ApyHub** → Convert script to audio (Azure Neural Voices)\n3. **OpenAI (GPT-4)** → Create visual scene prompts\n4. **PiAPI (Midjourney)** → Generate images with anime style\n5. **Hedra** → Combine audio + image into video\n6. **Google Drive** → Upload final video\n\n## Required APIs:\n- OpenAI API Key\n- ApyHub API Key\n- PiAPI API Key\n- Hedra API Key\n- ImgBB API Key (for image hosting)\n\n## Output:\n- TikTok-ready videos (9:16 aspect ratio)\n- Anime-style moving images\n- High-quality audio narration\n- Automated Google Drive upload", "height": 680, "width": 800}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-340, -4100], "id": "535150ee-21f5-422a-b7f8-33c471a7b349", "name": "Workflow Overview"}, {"parameters": {"content": "## OpenAI API Setup\n\n**Purpose**: Story generation & prompt engineering using GPT-4\n\n**Where to get API Key**:\n1. Visit: https://platform.openai.com\n2. Go to API Keys section\n3. Create new secret key\n4. Add billing method for GPT-4 access\n\n**Cost**: ~$0.03 per 1K tokens (GPT-4)\n\n**Features**:\n• Viral TikTok story generation\n• Scene-to-visual prompt transformation\n• Baby transformation prompts\n• Advanced prompt engineering", "height": 400, "width": 600, "color": 2}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1340, -3700], "id": "271401bc-64d2-41f8-ae09-b94ecd501122", "name": "OpenAI Info"}, {"parameters": {"content": "## ApyHub API Setup\n\n**Purpose**: Text-to-audio conversion using Azure Neural Voices\n\n**Where to get API Key**:\n1. Visit: https://apyhub.com\n2. Go to Workspace → API Keys\n3. Generate new API key\n\n**Cost**: ~$0.0001 per character\n\n**Features**:\n• Azure Neural Voices (<PERSON>, <PERSON>, <PERSON>, <PERSON>)\n• High-quality TTS\n• MP3 output format\n• Multiple voice options", "height": 400, "width": 600, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-740, -3700], "id": "a8e667b3-86b4-4ddd-8688-62e6a6c0e85c", "name": "ApyHub Info"}, {"parameters": {"content": "## PiAPI (Midjourney) Setup\n\n**Purpose**: Real Midjourney image generation & description\n\n**Where to get API Key**:\n1. Visit: https://piapi.ai\n2. Go to Workspace → API Keys\n3. Generate new API key\n4. Add billing/credits\n\n**Cost**: ~$0.05-0.10 per image\n\n**Features**:\n• Real Midjourney describe function\n• High-quality image generation\n• Anime style optimization\n• 9:16 aspect ratio support", "height": 400, "width": 600, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-140, -3700], "id": "9d72d17f-8081-4b07-ac18-b091c6ddd271", "name": "PiAPI Info"}, {"parameters": {"content": "## Hedra API Setup\n\n**Purpose**: Video assembly from audio + image\n\n**Where to get API Key**:\n1. Visit: https://www.hedra.com/api-profile\n2. Sign up and purchase plan\n3. Get API key from dashboard\n\n**Cost**: ~$0.10-0.50 per video\n\n**Features**:\n• Audio + image combination\n• Anime-style animation\n• 9:16 TikTok format\n• Professional quality output", "height": 400, "width": 600, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [480, -3700], "id": "8640259a-895a-4de0-9fc5-618bf0499516", "name": "<PERSON><PERSON>"}, {"parameters": {"rule": {"interval": [{}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-1700, -3100], "id": "728e9df1-3723-4bda-a856-db26a7916cef", "name": "Schedule Trigger"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4", "mode": "list", "cachedResultName": "GPT-4"}, "messages": {"values": [{"content": "You are an expert TikTok storyteller creating audio-first content. Your stories will be converted to speech, so write ONLY the spoken narration without any stage directions, camera instructions, or visual cues. Focus on compelling spoken storytelling that works purely through voice.", "role": "system"}, {"content": "Create a captivating TikTok story for voice narration about {{ $json.topic || 'a mysterious adventure' }}. Requirements:\n\n1. 45-60 seconds when spoken aloud\n2. Hook listeners in the first 5 seconds\n3. Use conversational, engaging language\n4. Tell a complete story with emotional impact\n5. End with intrigue or cliffhanger\n6. Perfect for anime-style visuals\n\nIMPORTANT: Write ONLY the spoken words - no stage directions, no (dramatic pause), no camera instructions, no visual cues. Just pure storytelling that works through voice alone.\n\nReturn only the narration text."}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-1480, -2920], "id": "8d56780c-b57d-4f9d-9a12-451d1e80b323", "name": "Generate TikTok Story", "credentials": {"openAiApi": {"id": "r8GhoyiGnQbw3pgu", "name": "OpenAi account"}}}, {"parameters": {"jsCode": "// Clean the OpenAI story content for audio conversion\nconst rawContent = items[0].json.content;\n\n// Remove all script directions and stage instructions\nlet cleanText = rawContent\n  // Remove parenthetical directions like (dramatic pause), (camera close up), etc.\n  .replace(/\\([^)]*\\)/g, '')\n  // Remove quotes around speech\n  .replace(/\"/g, '')\n  // Remove stage directions in brackets\n  .replace(/\\[[^\\]]*\\]/g, '')\n  // Remove newlines and extra spaces\n  .replace(/\\n+/g, ' ')\n  .replace(/\\s+/g, ' ')\n  // Remove common video script terms\n  .replace(/\\b(NARRATOR:|voice-over|Visual effects:|Background music|camera|close up|wide shot|zoom|fade|cut to)\\b[^.!?]*[.!?]?/gi, '')\n  // Clean up any remaining artifacts\n  .replace(/[\\s,]+/g, ' ')\n  .trim();\n\n// Ensure it ends with proper punctuation\nif (!cleanText.match(/[.!?]$/)) {\n  cleanText += '.';\n}\n\nreturn [{\n  json: {\n    content: items[0].json.content, // Keep original for visual prompt\n    cleanText: cleanText // Clean version for audio\n  }\n}];", "options": {}}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1000, 1100], "id": "clean-story-text", "name": "Clean Story for Audio"}, {"parameters": {"method": "POST", "url": "https://api.apyhub.com/generate/text/audio/file", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apy-token", "value": "APY0usrhHvEpR4zgIK2wJzryaukImSYGWMaUCA0y04M0wD2ihw6weV9TQLdRUpdGNhHj"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"text\": \"{{ $json.cleanText }}\",\n  \"gender\": \"female\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-1140, -3100], "id": "9428c55b-d863-4983-95a2-6a199a1fabc8", "name": "ApyHub - Text to Audio"}, {"parameters": {"amount": 30}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [-940, -3100], "id": "992f3aa7-3411-4e9e-9805-f1a01fccdc9a", "name": "Wait for Audio", "webhookId": "wait-audio-processing"}, {"parameters": {"url": "={{ $json.data.url }}", "options": {"response": {"response": {"responseFormat": "file"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-740, -3100], "id": "6dd245ca-6f36-4091-9b3c-47de98c7d4b0", "name": "Download Audio File"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4", "mode": "list", "cachedResultName": "GPT-4"}, "messages": {"values": [{"content": "You are an expert prompt engineer specializing in creating anime-style visual prompts for TikTok content. Transform story scripts into vivid, cinematic visual descriptions optimized for Midjourney image generation.", "role": "system"}, {"content": "Transform this TikTok story script into a compelling visual prompt for anime-style image generation:\n\nScript: {{ $('Generate TikTok Story').item.json.content }}\n\nCreate a visual prompt that:\n1. Captures the main character/scene from the story\n2. Uses anime art style with dramatic lighting\n3. Optimized for 9:16 aspect ratio (vertical)\n4. Includes cinematic composition\n5. Emphasizes emotional expression and atmosphere\n6. Suitable for animation/movement\n\nReturn only the visual prompt, no explanations. Keep under 500 characters."}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-700, -2900], "id": "133c249b-e1a3-4b48-a840-c57f7323828c", "name": "Create Visual Prompt", "credentials": {"openAiApi": {"id": "r8GhoyiGnQbw3pgu", "name": "OpenAi account"}}}, {"parameters": {"method": "POST", "url": "https://api.piapi.ai/api/v1/task", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "x-api-key", "value": "6d9c3e22532462701f580ce41a4e03100037876de129c4d8d359c6c18da3996c"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"model\": \"midjourney\",\n  \"task_type\": \"imagine\",\n  \"input\": {\n    \"prompt\": \"anime style {{ $json.content }}, cinematic lighting, detailed artwork --ar 9:16 --v 6\",\n    \"aspect_ratio\": \"9:16\",\n    \"process_mode\": \"fast\",\n    \"skip_prompt_check\": true\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-340, -3100], "id": "6cfa7b31-80e2-4ac4-99db-a0bdec1e0f95", "name": "PiAPI - Generate Image"}, {"parameters": {"unit": "minutes"}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [-140, -3100], "id": "192e4124-4127-45ee-ab61-fcf03c64c8ed", "name": "Wait for Image", "webhookId": "wait-image-generation"}, {"parameters": {"url": "=https://api.piapi.ai/api/v1/task/{{ $json.data.task_id }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "x-api-key", "value": "6d9c3e22532462701f580ce41a4e03100037876de129c4d8d359c6c18da3996c"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [60, -3100], "id": "e27a7b50-f97d-444c-8199-0b34dea0bf5b", "name": "Get Image Result"}, {"parameters": {"url": "={{ $json.data.output.image_url }}", "options": {"response": {"response": {"responseFormat": "file"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [260, -3100], "id": "20316df7-671a-40f8-b420-939e5040e3fb", "name": "Download Generated Image"}, {"parameters": {"method": "POST", "url": "https://api.hedra.com/web-app/public/assets", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Api-Key", "value": "hd_sk_b8f7e6d5c4a3b2e1f9g8h7i6j5k4l3m2n1o0p9q8r7s6t5u4v3w2x1y0z"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"name\": \"tiktok-image\",\n  \"type\": \"image\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [480, -3200], "id": "bfa0f318-3749-4fa9-b2c8-aa1fa6f2e823", "name": "Hedra - Create Image Asset"}, {"parameters": {"method": "POST", "url": "https://api.hedra.com/web-app/public/assets", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Api-Key", "value": "hd_sk_b8f7e6d5c4a3b2e1f9g8h7i6j5k4l3m2n1o0p9q8r7s6t5u4v3w2x1y0z"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"name\": \"tiktok-audio\",\n  \"type\": \"audio\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [480, -3000], "id": "582bef35-99ee-48fb-b443-6dfb4e6ba170", "name": "Hedra - Create Audio Asset"}, {"parameters": {}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [680, -3100], "id": "1e227eb7-add7-4a18-839c-3c7a9e05aa69", "name": "<PERSON><PERSON>"}, {"parameters": {"jsCode": "const imageAsset = items.find(item => item.json.type === 'image');\nconst audioAsset = items.find(item => item.json.type === 'audio');\nconst imageBinary = items.find(item => item.binary && item.binary.data);\nconst audioBinary = items.find(item => item.binary && item.binary.data && item.json.type !== 'image');\n\nreturn [\n  {\n    json: {\n      imageAssetId: imageAsset?.json.id,\n      audioAssetId: audioAsset?.json.id\n    },\n    binary: {\n      image: imageBinary?.binary.data,\n      audio: audioBinary?.binary.data\n    }\n  }\n];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [880, -3100], "id": "529ef5fd-7a71-4d20-ae17-c38d643c03bf", "name": "Prepare <PERSON>"}, {"parameters": {"method": "POST", "url": "=https://api.hedra.com/web-app/public/assets/{{ $json.imageAssetId }}/upload", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Api-Key", "value": "hd_sk_b8f7e6d5c4a3b2e1f9g8h7i6j5k4l3m2n1o0p9q8r7s6t5u4v3w2x1y0z"}, {"name": "Content-Type", "value": "multipart/form-data"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"parameterType": "formBinaryData", "name": "file", "inputDataFieldName": "image"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1080, -3200], "id": "bf5f841d-6eb3-4a2b-b846-67593fa34528", "name": "Upload Image to <PERSON><PERSON>"}, {"parameters": {"method": "POST", "url": "=https://api.hedra.com/web-app/public/assets/{{ $json.audioAssetId }}/upload", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Api-Key", "value": "hd_sk_b8f7e6d5c4a3b2e1f9g8h7i6j5k4l3m2n1o0p9q8r7s6t5u4v3w2x1y0z"}, {"name": "Content-Type", "value": "multipart/form-data"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"parameterType": "formBinaryData", "name": "file", "inputDataFieldName": "audio"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1080, -3000], "id": "5aa998b9-4916-4060-9229-0dea0ab56a75", "name": "Upload Audio to <PERSON>dra"}, {"parameters": {}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [1280, -3100], "id": "1a3be0d8-9026-4e3a-9fd5-36c0e20f7e21", "name": "Merge Uploaded Assets"}, {"parameters": {"jsCode": "const imageUpload = items.find(item => item.json && item.json.id && item.json.type === 'image');\nconst audioUpload = items.find(item => item.json && item.json.id && item.json.type === 'audio');\n\nreturn [\n  {\n    json: {\n      imageAssetId: imageUpload?.json.id,\n      audioAssetId: audioUpload?.json.id\n    }\n  }\n];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1480, -3100], "id": "720518a9-af6b-4aac-ad9b-80d51132cabc", "name": "Extract Asset IDs"}, {"parameters": {"method": "POST", "url": "https://api.hedra.com/web-app/public/generations", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Api-Key", "value": "hd_sk_b8f7e6d5c4a3b2e1f9g8h7i6j5k4l3m2n1o0p9q8r7s6t5u4v3w2x1y0z"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"type\": \"video\",\n  \"ai_model_id\": \"d1dd37a3-e39a-4854-a298-6510289f9cf2\",\n  \"start_keyframe_id\": \"{{ $json.imageAssetId }}\",\n  \"audio_id\": \"{{ $json.audioAssetId }}\",\n  \"generated_video_inputs\": {\n    \"text_prompt\": \"Anime-style character speaking with natural expressions and subtle movements, cinematic lighting, TikTok vertical format\",\n    \"resolution\": \"720p\",\n    \"aspect_ratio\": \"9:16\",\n    \"duration_ms\": 10000\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1680, -3100], "id": "a58529eb-a703-4f5f-9fa9-5e56be4e6761", "name": "Hedra - Generate Video"}, {"parameters": {"amount": 8, "unit": "minutes"}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [1880, -3100], "id": "1b36ad1b-b92b-4c9c-b915-35b5e847a86d", "name": "Wait for Video", "webhookId": "wait-video-generation"}, {"parameters": {"url": "=https://api.hedra.com/web-app/public/assets?type=video&ids={{ $json.asset_id }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Api-Key", "value": "hd_sk_b8f7e6d5c4a3b2e1f9g8h7i6j5k4l3m2n1o0p9q8r7s6t5u4v3w2x1y0z"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2080, -3100], "id": "6e81c4dc-cb3b-4bb5-9747-690da52ab5d5", "name": "Get Video Result"}, {"parameters": {"url": "={{ $json.asset.url }}", "options": {"response": {"response": {"responseFormat": "file"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2280, -3100], "id": "12a14bb6-baf2-4d4b-835b-418b1544b493", "name": "Download Final Video"}, {"parameters": {"name": "=tiktok-video-{{ new Date().getTime() }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "value": "REPLACE_WITH_YOUR_FOLDER_ID", "mode": "list", "cachedResultName": "TikTok Videos", "cachedResultUrl": "https://drive.google.com/drive/folders/REPLACE_WITH_YOUR_FOLDER_ID"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [2480, -3100], "id": "103a87a1-75b4-4c4b-bba7-d1fe76618ad6", "name": "Upload to Google Drive", "credentials": {"googleDriveOAuth2Api": {"id": "aJWVPXXWME5PxqFM", "name": "Google Drive account"}}}, {"parameters": {"content": "# Video Generation Complete!\n\n✅ **TikTok Video Successfully Created**\n\n**Pipeline Steps Completed:**\n1. ✅ Story generated with OpenAI GPT-4\n2. ✅ Audio created with ApyHub (Azure Neural Voices)\n3. ✅ Visual prompt created with OpenAI GPT-4\n4. ✅ Image generated with PiAPI (Midjourney)\n5. ✅ Video assembled with <PERSON><PERSON>\n6. ✅ Uploaded to Google Drive\n\n**Output:**\n- Format: MP4 (9:16 aspect ratio)\n- Duration: ~60 seconds\n- Style: Anime with natural movement\n- Quality: 720p TikTok-ready\n\n**Next Steps:**\n- Download from Google Drive\n- Upload to TikTok\n- Add captions/hashtags\n- Schedule posting", "height": 600, "width": 500, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2680, -3300], "id": "0d2ae8ea-1eb6-4b01-86e3-b90334358488", "name": "Completion Note"}], "pinData": {}, "connections": {"Schedule Trigger": {"main": [[{"node": "Generate TikTok Story", "type": "main", "index": 0}]]}, "Generate TikTok Story": {"main": [[{"node": "Clean Story for Audio", "type": "main", "index": 0}, {"node": "Create Visual Prompt", "type": "main", "index": 0}]]}, "Clean Story for Audio": {"main": [[{"node": "ApyHub - Text to Audio", "type": "main", "index": 0}]]}, "ApyHub - Text to Audio": {"main": [[{"node": "Wait for Audio", "type": "main", "index": 0}]]}, "Wait for Audio": {"main": [[{"node": "Download Audio File", "type": "main", "index": 0}]]}, "Download Audio File": {"main": [[{"node": "Hedra - Create Audio Asset", "type": "main", "index": 0}]]}, "Create Visual Prompt": {"main": [[{"node": "PiAPI - Generate Image", "type": "main", "index": 0}]]}, "PiAPI - Generate Image": {"main": [[{"node": "Wait for Image", "type": "main", "index": 0}]]}, "Wait for Image": {"main": [[{"node": "Get Image Result", "type": "main", "index": 0}]]}, "Get Image Result": {"main": [[{"node": "Download Generated Image", "type": "main", "index": 0}]]}, "Download Generated Image": {"main": [[{"node": "Hedra - Create Image Asset", "type": "main", "index": 0}]]}, "Hedra - Create Image Asset": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Hedra - Create Audio Asset": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Merge Assets": {"main": [[{"node": "Prepare <PERSON>", "type": "main", "index": 0}]]}, "Prepare Assets": {"main": [[{"node": "Upload Image to <PERSON><PERSON>", "type": "main", "index": 0}, {"node": "Upload Audio to <PERSON>dra", "type": "main", "index": 0}]]}, "Upload Image to Hedra": {"main": [[{"node": "Merge Uploaded Assets", "type": "main", "index": 0}]]}, "Upload Audio to Hedra": {"main": [[{"node": "Merge Uploaded Assets", "type": "main", "index": 1}]]}, "Merge Uploaded Assets": {"main": [[{"node": "Extract Asset IDs", "type": "main", "index": 0}]]}, "Extract Asset IDs": {"main": [[{"node": "Hedra - Generate Video", "type": "main", "index": 0}]]}, "Hedra - Generate Video": {"main": [[{"node": "Wait for Video", "type": "main", "index": 0}]]}, "Wait for Video": {"main": [[{"node": "Get Video Result", "type": "main", "index": 0}]]}, "Get Video Result": {"main": [[{"node": "Download Final Video", "type": "main", "index": 0}]]}, "Download Final Video": {"main": [[{"node": "Upload to Google Drive", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "11b90613-8a2c-45dd-ac48-0cb33d1caf2f", "meta": {"instanceId": "654819242addc232facd1c56106aee4f2a1663769043e48276f539d94d097b62"}, "id": "VXTxypTMbxwLk9TZ", "tags": [{"name": "tiktok-automation", "id": "qY2rNxHX4pvpvedF", "createdAt": "2025-06-01T03:27:59.885Z", "updatedAt": "2025-06-01T03:27:59.885Z"}]}