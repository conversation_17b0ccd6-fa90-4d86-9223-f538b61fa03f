"""
Video Generator for TikTok Automation
Combines audio, images, and effects to create TikTok videos
"""

import os
import logging
import time
from typing import Dict, Any, Optional, List, Tuple
import tempfile
import json
import shutil

# For video generation
MOVIEPY_AVAILABLE = False
try:
    import moviepy
    # Try importing from moviepy.editor first (newer versions)
    try:
        from moviepy.editor import (
            AudioFileClip, ImageClip, TextClip, CompositeVideoClip,
            concatenate_videoclips, vfx
        )
    except ImportError:
        # Fallback to direct import from moviepy (older versions)
        from moviepy import (
            AudioFileClip, ImageClip, TextClip, CompositeVideoClip,
            concatenate_videoclips, vfx
        )

    # Try importing subtitles tools
    try:
        from moviepy.video.tools.subtitles import SubtitlesClip
    except ImportError:
        SubtitlesClip = None

    MOVIEPY_AVAILABLE = True
    logging.info("MoviePy package successfully imported.")
except ImportError as e:
    logging.warning(f"MoviePy package not installed. Video generation will not be available. Error: {str(e)}")
    # Define dummy classes to prevent NameError
    AudioFileClip = None
    ImageClip = None
    TextClip = None
    CompositeVideoClip = None

class VideoGenerator:
    """Generates TikTok videos by combining audio, images, and effects"""

    def __init__(self, config: Dict[str, Any]):
        """Initialize video generator

        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.width = config['video']['resolution']['width']
        self.height = config['video']['resolution']['height']
        self.fps = config['video']['fps']
        self.format = config['video']['format']
        self.effects_enabled = config['video']['effects']['enabled']
        self.default_effect = config['video']['effects']['default']
        self.subtitles_enabled = config['video']['subtitles']['enabled']
        self.subtitle_settings = config['video']['subtitles']

        # Create output directory
        self.output_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
                                      "data", "videos")
        os.makedirs(self.output_dir, exist_ok=True)

    def _check_moviepy_availability(self):
        """Check if MoviePy is available for video generation

        Raises:
            ImportError: If MoviePy is not available
        """
        if not MOVIEPY_AVAILABLE:
            raise ImportError(
                "MoviePy is not installed or failed to import. "
                "Please install it using: pip install moviepy"
            )

    def _set_duration(self, clip, duration):
        """Set duration on a clip, handling API differences between MoviePy versions

        Args:
            clip: MoviePy clip
            duration: Duration in seconds

        Returns:
            Clip with duration set
        """
        if hasattr(clip, 'with_duration'):
            # New MoviePy API (2.x)
            return clip.with_duration(duration)
        elif hasattr(clip, 'set_duration'):
            # Old MoviePy API (1.x)
            return clip.set_duration(duration)
        else:
            # Fallback - try both
            try:
                return clip.with_duration(duration)
            except AttributeError:
                return clip.set_duration(duration)

    def _set_audio(self, clip, audio):
        """Set audio on a clip, handling API differences between MoviePy versions

        Args:
            clip: MoviePy clip
            audio: Audio clip

        Returns:
            Clip with audio set
        """
        if hasattr(clip, 'with_audio'):
            # New MoviePy API (2.x)
            return clip.with_audio(audio)
        elif hasattr(clip, 'set_audio'):
            # Old MoviePy API (1.x)
            return clip.set_audio(audio)
        else:
            # Fallback - try both
            try:
                return clip.with_audio(audio)
            except AttributeError:
                return clip.set_audio(audio)

    def _set_position(self, clip, position):
        """Set position on a clip, handling API differences between MoviePy versions"""
        if hasattr(clip, 'with_position'):
            return clip.with_position(position)
        elif hasattr(clip, 'set_position'):
            return clip.set_position(position)
        else:
            try:
                return clip.with_position(position)
            except AttributeError:
                return clip.set_position(position)

    def _set_start(self, clip, start_time):
        """Set start time on a clip, handling API differences between MoviePy versions"""
        if hasattr(clip, 'with_start'):
            return clip.with_start(start_time)
        elif hasattr(clip, 'set_start'):
            return clip.set_start(start_time)
        else:
            try:
                return clip.with_start(start_time)
            except AttributeError:
                return clip.set_start(start_time)

    def _set_end(self, clip, end_time):
        """Set end time on a clip, handling API differences between MoviePy versions"""
        if hasattr(clip, 'with_end'):
            return clip.with_end(end_time)
        elif hasattr(clip, 'set_end'):
            return clip.set_end(end_time)
        else:
            try:
                return clip.with_end(end_time)
            except AttributeError:
                return clip.set_end(end_time)

    def _resize(self, clip, *args, **kwargs):
        """Resize a clip, handling API differences between MoviePy versions"""
        if hasattr(clip, 'resized'):
            # New MoviePy API (2.x)
            return clip.resized(*args, **kwargs)
        elif hasattr(clip, 'resize'):
            # Old MoviePy API (1.x)
            return clip.resize(*args, **kwargs)
        else:
            try:
                return clip.resized(*args, **kwargs)
            except AttributeError:
                return clip.resize(*args, **kwargs)

    def _create_text_clip(self, text, **kwargs):
        """Create a TextClip, handling API differences between MoviePy versions"""
        # Handle font size parameter name change
        if 'fontsize' in kwargs:
            font_size = kwargs['fontsize']
        elif 'font_size' in kwargs:
            font_size = kwargs['font_size']
        else:
            font_size = 50  # Default font size

        # Build parameters dict (without 'text' as keyword)
        text_kwargs = {}

        # Handle other parameters
        for key, value in kwargs.items():
            if key not in ['fontsize']:  # Skip old parameter names
                if key == 'align':
                    # In newer versions, 'align' might be 'text_align'
                    text_kwargs['text_align'] = value
                elif key == 'method':
                    # Keep method parameter
                    text_kwargs['method'] = value
                elif key == 'size':
                    # Keep size parameter
                    text_kwargs['size'] = value
                elif key == 'font':
                    # Handle font parameter - use None for default system font
                    if value and value.lower() != 'arial':
                        text_kwargs['font'] = value
                    # Skip Arial as it might not be available, use default
                else:
                    text_kwargs[key] = value

        # Add font_size
        text_kwargs['fontsize'] = font_size

        try:
            # Try with text as positional argument (newer MoviePy)
            return TextClip(text, **text_kwargs)
        except (TypeError, ValueError, OSError) as e:
            # If there are still parameter issues, try with minimal parameters
            logging.warning(f"TextClip creation failed with full parameters: {e}")
            minimal_kwargs = {
                'fontsize': font_size,
                'color': text_kwargs.get('color', 'white'),
                'stroke_color': text_kwargs.get('stroke_color', 'black'),
                'stroke_width': text_kwargs.get('stroke_width', 2)
            }
            try:
                return TextClip(text, **minimal_kwargs)
            except (ValueError, OSError) as e2:
                # Last resort: create with absolute minimal parameters
                logging.warning(f"TextClip creation failed with minimal parameters: {e2}")
                return TextClip(text, fontsize=50, color='white')

    def generate_video(self, story_text: str, audio_path: str, image_path: str,
                       output_filename: Optional[str] = None, effect: Optional[str] = None) -> Dict[str, Any]:
        """Generate a TikTok video

        Args:
            story_text: Text of the story for subtitles
            audio_path: Path to the audio file
            image_path: Path to the cover image
            output_filename: Optional filename for the output video
            effect: Visual effect to apply (default: use config default)

        Returns:
            Dict: Video metadata including path and duration
        """
        # Check if MoviePy is available
        self._check_moviepy_availability()

        logging.info(f"Generating video with audio: {audio_path} and image: {image_path}")

        # Generate filename if not provided
        if not output_filename:
            timestamp = int(time.time())
            output_filename = f"video_{timestamp}.{self.format}"

        output_path = os.path.join(self.output_dir, output_filename)

        try:
            # Load audio clip
            audio_clip = AudioFileClip(audio_path)
            duration = audio_clip.duration

            # Load image clip and set duration
            image_clip = ImageClip(image_path)
            image_clip = self._set_duration(image_clip, duration)

            # Resize image to match video dimensions
            image_clip = self._resize(image_clip, height=self.height, width=self.width)

            # Apply visual effect if enabled
            if self.effects_enabled:
                effect_name = effect or self.default_effect
                image_clip = self._apply_effect(image_clip, effect_name)

            # Set audio
            video_clip = self._set_audio(image_clip, audio_clip)

            # Add subtitles if enabled
            if self.subtitles_enabled:
                video_clip = self._add_subtitles(video_clip, story_text)

            # Write video file
            video_clip.write_videofile(
                output_path,
                fps=self.fps,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile=os.path.join(tempfile.gettempdir(), "temp-audio.m4a"),
                remove_temp=True
            )

            # Clean up
            video_clip.close()
            audio_clip.close()

            # Generate metadata
            metadata = {
                'path': output_path,
                'filename': output_filename,
                'duration': duration,
                'resolution': f"{self.width}x{self.height}",
                'fps': self.fps,
                'format': self.format,
                'audio_path': audio_path,
                'image_path': image_path,
                'effect': effect or self.default_effect,
                'subtitles': self.subtitles_enabled,
                'timestamp': time.time()
            }

            logging.info(f"Generated video saved to: {output_path} (duration: {duration:.2f}s)")
            return metadata
        except Exception as e:
            logging.error(f"Error generating video: {str(e)}")
            raise

    def _apply_effect(self, clip, effect_name: str):
        """Apply visual effect to the clip

        Args:
            clip: MoviePy clip
            effect_name: Name of the effect to apply

        Returns:
            MoviePy clip with effect applied
        """
        if effect_name == 'zoom':
            return self._apply_zoom_effect(clip)
        elif effect_name == 'pan':
            return self._apply_pan_effect(clip)
        elif effect_name == 'fade':
            return self._apply_fade_effect(clip)
        elif effect_name == 'pulse':
            return self._apply_pulse_effect(clip)
        else:
            logging.warning(f"Unknown effect: {effect_name}, using zoom")
            return self._apply_zoom_effect(clip)

    def _apply_zoom_effect(self, clip):
        """Apply slow zoom-in effect

        Args:
            clip: MoviePy clip

        Returns:
            MoviePy clip with zoom effect
        """
        def zoom(t):
            # Slow zoom from 1.0 to 1.1 scale
            return 1 + 0.1 * t / clip.duration

        return self._resize(clip, lambda t: zoom(t))

    def _apply_pan_effect(self, clip):
        """Apply panning effect

        Args:
            clip: MoviePy clip

        Returns:
            MoviePy clip with pan effect
        """
        # Create a clip that's larger than the final video
        zoomed_clip = self._resize(clip, 1.3)

        # Function to move the position over time (left to right)
        def pan_position(t):
            # Move from left to right
            progress = t / clip.duration
            x_position = (zoomed_clip.w - self.width) * progress
            return ('center', 'center')

        return self._set_position(zoomed_clip, pan_position)

    def _apply_fade_effect(self, clip):
        """Apply fade in/out effect

        Args:
            clip: MoviePy clip

        Returns:
            MoviePy clip with fade effect
        """
        # Add fade in and fade out
        return clip.fadein(1.5).fadeout(1.5)

    def _apply_pulse_effect(self, clip):
        """Apply pulsing zoom effect

        Args:
            clip: MoviePy clip

        Returns:
            MoviePy clip with pulse effect
        """
        def pulse_zoom(t):
            # Pulsing zoom effect using sine wave
            import math
            return 1 + 0.05 * math.sin(2 * math.pi * t / 5)  # 5-second cycle

        return self._resize(clip, lambda t: pulse_zoom(t))

    def _add_subtitles(self, clip, text: str):
        """Add subtitles to the video

        Args:
            clip: MoviePy clip
            text: Text for subtitles

        Returns:
            MoviePy clip with subtitles
        """
        # Generate subtitle segments
        segments = self._generate_subtitle_segments(text, clip.duration)

        # Create subtitle clips
        subtitle_clips = []

        for segment in segments:
            start_time, end_time, segment_text = segment

            # Create text clip using compatibility wrapper with proper type conversion
            txt_clip = self._create_text_clip(
                segment_text,
                fontsize=self.subtitle_settings['size'],
                font=self.subtitle_settings['font'],
                color=self.subtitle_settings['color'],
                stroke_color=self.subtitle_settings['stroke_color'],
                stroke_width=self.subtitle_settings['stroke_width'],
                method='caption',
                align='center',
                size=(int(self.width * 0.9), None)  # Ensure integer width
            )

            # Set position and duration with proper type conversion
            txt_clip = self._set_position(txt_clip, ('center', int(self.height - 150)))
            txt_clip = self._set_start(txt_clip, float(start_time))
            txt_clip = self._set_end(txt_clip, float(end_time))

            subtitle_clips.append(txt_clip)

        # Combine with original clip
        return CompositeVideoClip([clip] + subtitle_clips)

    def _generate_subtitle_segments(self, text: str, duration: float) -> List[Tuple[float, float, str]]:
        """Generate subtitle segments with timing

        Args:
            text: Full text
            duration: Total duration of the video

        Returns:
            List of (start_time, end_time, text) tuples
        """
        # Split text into sentences
        sentences = []
        for sentence in text.replace('. ', '.\n').replace('! ', '!\n').replace('? ', '?\n').split('\n'):
            if sentence.strip():
                sentences.append(sentence.strip())

        # Calculate approximate time per sentence
        if not sentences:
            return []

        time_per_sentence = duration / len(sentences)

        # Generate segments
        segments = []
        current_time = 0

        for sentence in sentences:
            # Adjust time based on sentence length
            sentence_time = time_per_sentence * (len(sentence) / 100 + 0.5)  # Adjust for sentence length
            sentence_time = min(max(2.0, sentence_time), 5.0)  # Between 2 and 5 seconds

            # Ensure we don't exceed total duration
            end_time = min(current_time + sentence_time, duration)

            segments.append((current_time, end_time, sentence))
            current_time = end_time

        return segments

    def export_metadata(self, video_metadata: Dict[str, Any], output_path: Optional[str] = None) -> str:
        """Export video metadata to JSON file

        Args:
            video_metadata: Video metadata dictionary
            output_path: Optional path for the metadata file

        Returns:
            str: Path to the metadata file
        """
        if not output_path:
            video_path = video_metadata['path']
            output_path = os.path.splitext(video_path)[0] + '.json'

        try:
            with open(output_path, 'w') as f:
                json.dump(video_metadata, f, indent=4)

            logging.info(f"Exported video metadata to: {output_path}")
            return output_path
        except Exception as e:
            logging.error(f"Error exporting video metadata: {str(e)}")
            raise
