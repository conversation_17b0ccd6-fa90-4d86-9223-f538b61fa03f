#!/usr/bin/env python3
"""
Simple Test for Real APIs
"""

import json
import sys
import os

# Add project root to path
sys.path.insert(0, os.getcwd())

def test_api_components():
    """Test individual API components"""
    print("🧪 Testing Real API Components...")
    print("=" * 50)
    
    # Load config
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
        print("✅ Configuration loaded")
    except Exception as e:
        print(f"❌ Error loading config: {e}")
        return False
    
    # Test ApyHub Audio Tools
    print("\n1. Testing ApyHub Audio Tools...")
    try:
        from src.ai.audio_tools import ApyHubAudio
        audio_tools = ApyHubAudio(config)
        print("   ✅ ApyHub audio tools initialized")
        print(f"   📡 API Key: {audio_tools.api_key[:10]}...{audio_tools.api_key[-4:]}")
        print(f"   🔗 Endpoint: {audio_tools.tts_endpoint}")
        print(f"   🎙️ Available voices: {list(audio_tools.voice_mapping.keys())}")
    except Exception as e:
        print(f"   ❌ ApyHub initialization failed: {e}")
        return False
    
    # Test PiAPI Image Tools
    print("\n2. Testing PiAPI Image Tools...")
    try:
        from src.ai.image_tools import PiAPIMidjourneyTools
        image_tools = PiAPIMidjourneyTools(config)
        print("   ✅ PiAPI image tools initialized")
        print(f"   📡 API Key: {image_tools.api_key[:10]}...{image_tools.api_key[-4:]}")
        print(f"   🔗 Endpoint: {image_tools.task_endpoint}")
    except Exception as e:
        print(f"   ❌ PiAPI initialization failed: {e}")
        return False
    
    # Test Hedra Video Assembler
    print("\n3. Testing Hedra Video Assembler...")
    try:
        from src.ai.video_assembler import HedraVideoAssembler
        video_assembler = HedraVideoAssembler(config)
        print("   ✅ Hedra video assembler initialized")
        print(f"   📡 API Key: {video_assembler.api_key[:10]}...{video_assembler.api_key[-4:]}")
        print(f"   🔗 Base URL: {video_assembler.base_url}")
    except Exception as e:
        print(f"   ❌ Hedra initialization failed: {e}")
        return False
    
    # Test OpenAI Story Generator
    print("\n4. Testing OpenAI Story Generator...")
    try:
        from src.ai.story_generator import EnhancedStoryGenerator
        story_gen = EnhancedStoryGenerator(config)
        print("   ✅ OpenAI story generator initialized")
    except Exception as e:
        print(f"   ❌ OpenAI initialization failed: {e}")
        return False
    
    return True

def check_configuration():
    """Check if configuration is correct for real APIs"""
    print("\n🔍 Checking Configuration...")
    
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        # Check providers
        tts_provider = config.get('tts', {}).get('provider')
        image_provider = config.get('image', {}).get('provider')
        
        print(f"   TTS Provider: {tts_provider}")
        print(f"   Image Provider: {image_provider}")
        
        # Check API keys
        api_keys = config.get('api_keys', {})
        required_keys = ['openai', 'apyhub', 'piapi', 'hedra']
        
        keys_ok = True
        for key in required_keys:
            if api_keys.get(key):
                print(f"   ✅ {key.upper()} API key configured")
            else:
                print(f"   ❌ {key.upper()} API key missing")
                keys_ok = False
        
        # Check if enhanced workflow is enabled
        ai_workflow = config.get('ai_workflow', {})
        apyhub_enabled = ai_workflow.get('apyhub_tts', {}).get('enabled', False)
        piapi_enabled = ai_workflow.get('piapi_midjourney', {}).get('generation_enabled', False)
        hedra_enabled = ai_workflow.get('hedra_video', {}).get('enabled', False)
        
        print(f"   ApyHub TTS: {'✅ Enabled' if apyhub_enabled else '❌ Disabled'}")
        print(f"   PiAPI Midjourney: {'✅ Enabled' if piapi_enabled else '❌ Disabled'}")
        print(f"   Hedra Video: {'✅ Enabled' if hedra_enabled else '❌ Disabled'}")
        
        if (tts_provider == 'apyhub' and image_provider == 'piapi_midjourney' and 
            apyhub_enabled and piapi_enabled and hedra_enabled and keys_ok):
            print("\n✅ Configuration is PERFECT for real API usage!")
            return True
        else:
            print("\n⚠️ Configuration needs adjustment")
            return False
            
    except Exception as e:
        print(f"❌ Error checking config: {e}")
        return False

def main():
    """Main function"""
    print("🎬 TikTok Automation - Simple API Test")
    print("=" * 50)
    
    # Check configuration
    config_ok = check_configuration()
    
    # Test API components
    apis_ok = test_api_components()
    
    print("\n" + "=" * 50)
    print("🎯 TEST SUMMARY:")
    print(f"   Configuration: {'✅ OK' if config_ok else '❌ FAILED'}")
    print(f"   API Components: {'✅ OK' if apis_ok else '❌ FAILED'}")
    
    if config_ok and apis_ok:
        print("\n🎉 ALL TESTS PASSED!")
        print("\n🚀 Your app is ready to use REAL APIs:")
        print("   • ApyHub for human-like audio")
        print("   • PiAPI for Midjourney images")
        print("   • Hedra for video assembly")
        print("   • OpenAI for story generation")
        
        print("\n📋 Test Data for main.py:")
        print("   Account: MyTikTokTest")
        print("   Niche: horror")
        print("   Theme: Scary Stories")
        print("   Topic: A mirror that shows your future")
        print("   Voice: female")
        
        print("\n🎬 Expected Result:")
        print("   • High-quality human voice (ApyHub)")
        print("   • Beautiful Midjourney image (PiAPI)")
        print("   • Professional video (Hedra)")
        print("   • All APIs will be consumed!")
        
        print("\n▶️ RUN: python main.py")
        
    else:
        print("\n❌ TESTS FAILED")
        if not config_ok:
            print("   - Fix configuration issues")
        if not apis_ok:
            print("   - Fix API component issues")
    
    input("\nPress Enter to continue...")

if __name__ == "__main__":
    main()
