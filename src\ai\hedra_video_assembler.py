"""
Hedra Video Assembler - Correct Asset-Based Implementation
Generates animated videos using Hedra API with proper asset upload workflow
"""

import os
import logging
import time
import asyncio
import httpx
from typing import Dict, Any, Optional


class HedraVideoAssembler:
    """Hedra video assembler using correct asset-based API workflow"""

    def __init__(self, api_key: str, output_dir: str = "data/videos"):
        """Initialize Hedra video assembler

        Args:
            api_key: Hedra API key
            output_dir: Directory to save generated videos
        """
        self.api_key = api_key
        self.output_dir = output_dir
        self.client = httpx.AsyncClient(timeout=300.0)

        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)

        logging.info("Hedra video assembler initialized")

    async def _create_asset(self, asset_type: str, name: str) -> str:
        """Create a Hedra asset

        Args:
            asset_type: Type of asset ('image' or 'audio')
            name: Name for the asset

        Returns:
            str: Asset ID
        """
        payload = {
            "name": name,
            "type": asset_type
        }

        headers = {
            "X-Api-Key": self.api_key,
            "Content-Type": "application/json"
        }

        response = await self.client.post(
            "https://api.hedra.com/web-app/public/assets",
            json=payload,
            headers=headers
        )
        response.raise_for_status()

        result = response.json()
        asset_id = result["id"]
        logging.info(f"Created {asset_type} asset: {asset_id}")
        return asset_id

    async def _upload_asset(self, asset_id: str, file_path: str) -> None:
        """Upload binary data to a Hedra asset

        Args:
            asset_id: ID of the asset to upload to
            file_path: Path to the file to upload
        """
        headers = {
            "X-Api-Key": self.api_key
        }

        with open(file_path, 'rb') as f:
            files = {"file": f}
            response = await self.client.post(
                f"https://api.hedra.com/web-app/public/assets/{asset_id}/upload",
                files=files,
                headers=headers
            )
            response.raise_for_status()

        logging.info(f"Uploaded asset: {asset_id}")

    async def generate_video(self, image_path: str, audio_path: str,
                           output_filename: Optional[str] = None,
                           animation_style: str = "natural") -> Dict[str, Any]:
        """Generate animated video using Hedra API with 10s looping optimization

        Args:
            image_path: Path to the source image
            audio_path: Path to the audio file
            output_filename: Optional filename for output
            animation_style: Animation style (natural, dramatic, subtle, scary)

        Returns:
            Dict containing video generation result
        """
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"Image file not found: {image_path}")

        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"Audio file not found: {audio_path}")

        # Check for debug mode
        debug_enabled = os.getenv('DEBUG', 'false').lower() == 'true'
        hedra_debug = os.getenv('HEDRA_VIDEO_DEBUG', 'false').lower() == 'true'

        if debug_enabled and hedra_debug:
            logging.info("🔧 DEBUG MODE: Using pre-generated video instead of Hedra API")
            return await self._generate_debug_video(image_path, audio_path, output_filename, animation_style)

        logging.info(f"Generating video with Hedra (10s optimized): {image_path} + {audio_path}")

        # Generate filename if not provided
        if not output_filename:
            timestamp = int(time.time())
            output_filename = f"hedra_video_{timestamp}.mp4"

        output_path = os.path.join(self.output_dir, output_filename)

        try:
            # Step 1: Create assets
            logging.info("🔧 STEP 1: Creating Hedra assets...")
            try:
                image_asset_id = await self._create_asset("image", "scary-story-image")
                logging.info(f"✅ Image asset created: {image_asset_id}")
            except Exception as e:
                logging.error(f"❌ Failed to create image asset: {e}")
                raise

            try:
                audio_asset_id = await self._create_asset("audio", "scary-story-audio")
                logging.info(f"✅ Audio asset created: {audio_asset_id}")
            except Exception as e:
                logging.error(f"❌ Failed to create audio asset: {e}")
                raise

            # Step 2: Upload assets
            logging.info("🔧 STEP 2: Uploading assets to Hedra...")
            try:
                await self._upload_asset(image_asset_id, image_path)
                logging.info(f"✅ Image uploaded to asset: {image_asset_id}")
            except Exception as e:
                logging.error(f"❌ Failed to upload image: {e}")
                raise

            try:
                await self._upload_asset(audio_asset_id, audio_path)
                logging.info(f"✅ Audio uploaded to asset: {audio_asset_id}")
            except Exception as e:
                logging.error(f"❌ Failed to upload audio: {e}")
                raise

            # Step 3: Create animation prompt based on style
            animation_prompts = {
                "natural": "Natural subtle movements, gentle breathing, soft eye blinks, minimal head movement with calm expression",
                "dramatic": "Intense dramatic expressions, dynamic head movements, strong emotional reactions, cinematic lighting effects",
                "subtle": "Very subtle micro-expressions, gentle breathing, minimal movement with focused attention",
                "energetic": "Animated expressions, dynamic movements, engaging gestures, lively facial expressions",
                "calm": "Peaceful serene expressions, slow gentle movements, tranquil breathing, meditative focus",
                "scary": "Mysterious atmospheric movements, subtle tension in facial expressions, eerie ambient lighting, haunting gaze with slow deliberate movements"
            }

            animation_prompt = animation_prompts.get(animation_style, animation_prompts["scary"])
            logging.info(f"Using animation prompt: {animation_prompt}")

            # Step 3: Start video generation
            logging.info("🔧 STEP 3: Starting video generation...")
            payload = {
                "type": "video",
                "ai_model_id": "d1dd37a3-e39a-4854-a298-6510289f9cf2",
                "start_keyframe_id": image_asset_id,
                "audio_id": audio_asset_id,
                "generated_video_inputs": {
                    "text_prompt": animation_prompt,
                    "resolution": "720p",
                    "aspect_ratio": "9:16",
                    "duration_ms": 10000  # 10 seconds duration - MASSIVE CREDIT SAVINGS!
                }
            }

            logging.info(f"🔧 Payload: {payload}")

            headers = {
                "X-Api-Key": self.api_key,
                "Content-Type": "application/json"
            }

            response = await self.client.post(
                "https://api.hedra.com/web-app/public/generations",
                json=payload,
                headers=headers
            )

            logging.info(f"🔧 Hedra API response status: {response.status_code}")
            if response.status_code != 200:
                logging.error(f"❌ Hedra API error response: {response.text}")
                # Log the exact payload that failed
                logging.error(f"❌ Failed payload: {payload}")
                raise Exception(f"Hedra API error: {response.text}")

            response.raise_for_status()

            # Handle response
            result = response.json()
            generation_id = result.get("id") or result.get("generation_id") or result.get("asset_id")

            if not generation_id:
                logging.error(f"No generation ID in response: {result}")
                raise Exception(f"No generation ID in response: {result}")

            logging.info(f"Hedra generation started: {generation_id}")

            # Step 4: Wait for completion and download 10s video
            video_url = await self._wait_for_completion(generation_id)
            temp_10s_path = output_path.replace('.mp4', '_10s_temp.mp4')
            await self._download_video(video_url, temp_10s_path)

            logging.info("🔧 STEP 5: Creating 60s looped video from 10s Hedra animation...")

            # Step 5: Loop the 10s video to create full-length video
            final_output_path = await self._create_looped_video(temp_10s_path, audio_path, output_path)

            # Clean up temporary file
            try:
                os.remove(temp_10s_path)
                logging.info(f"🧹 Cleaned up temporary file: {temp_10s_path}")
            except:
                pass

            return {
                'path': final_output_path,
                'generation_id': generation_id,
                'animation_style': animation_style,
                'animation_prompt': animation_prompt,
                'timestamp': time.time(),
                'provider': 'Hedra (10s looped)',
                'original_duration': '10s',
                'final_duration': 'full_audio_length',
                'credit_optimization': 'enabled'
            }

        except Exception as e:
            logging.error(f"Error generating video with Hedra: {str(e)}")
            raise

    async def _wait_for_completion(self, generation_id: str, max_wait_time: int = 1200) -> str:
        """Wait for video generation to complete

        Args:
            generation_id: Generation ID to monitor
            max_wait_time: Maximum wait time in seconds

        Returns:
            str: URL of generated video
        """
        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            try:
                # Check generation status using assets endpoint
                response = await self.client.get(
                    f"https://api.hedra.com/web-app/public/assets?type=video&ids={generation_id}",
                    headers={"X-Api-Key": self.api_key}
                )
                response.raise_for_status()

                result = response.json()

                if result and "asset" in result and result["asset"].get("url"):
                    video_url = result["asset"]["url"]
                    logging.info(f"Video generation completed: {video_url}")
                    return video_url

                logging.info(f"Generation {generation_id}: Still processing...")
                await asyncio.sleep(15)  # Wait 15 seconds before next check

            except Exception as e:
                logging.error(f"Error checking generation status: {str(e)}")
                await asyncio.sleep(15)

        raise TimeoutError(f"Video generation timed out after {max_wait_time} seconds")

    async def _download_video(self, video_url: str, output_path: str) -> None:
        """Download generated video

        Args:
            video_url: URL of the generated video
            output_path: Local path to save the video
        """
        try:
            response = await self.client.get(video_url)
            response.raise_for_status()

            with open(output_path, 'wb') as f:
                f.write(response.content)

            logging.info(f"Video downloaded: {output_path}")

        except Exception as e:
            logging.error(f"Error downloading video: {str(e)}")
            raise

    async def _create_looped_video(self, video_10s_path: str, audio_path: str, output_path: str) -> str:
        """Create a looped video from 10s Hedra animation to match audio duration

        Args:
            video_10s_path: Path to the 10-second Hedra video
            audio_path: Path to the full audio file
            output_path: Path for the final output video

        Returns:
            str: Path to the final looped video
        """
        try:
            # Import MoviePy for video processing
            from moviepy.editor import VideoFileClip, AudioFileClip, concatenate_videoclips

            # Load the 10s video and full audio
            video_clip = VideoFileClip(video_10s_path)
            audio_clip = AudioFileClip(audio_path)

            # Calculate how many loops we need
            audio_duration = audio_clip.duration
            video_duration = video_clip.duration
            loops_needed = int(audio_duration / video_duration) + 1

            logging.info(f"🔄 Audio duration: {audio_duration:.1f}s, Video duration: {video_duration:.1f}s")
            logging.info(f"🔄 Creating {loops_needed} loops of the 10s animation")

            # Create looped video clips
            looped_clips = [video_clip] * loops_needed

            # Concatenate all loops
            looped_video = concatenate_videoclips(looped_clips)

            # Trim to exact audio duration
            looped_video = looped_video.subclip(0, audio_duration)

            # Set the full audio
            final_video = looped_video.set_audio(audio_clip)

            # Write the final video
            final_video.write_videofile(
                output_path,
                fps=30,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile='temp-audio.m4a',
                remove_temp=True,
                verbose=False,
                logger=None
            )

            # Clean up
            video_clip.close()
            audio_clip.close()
            looped_video.close()
            final_video.close()

            logging.info(f"✅ Looped video created: {output_path} ({audio_duration:.1f}s)")
            return output_path

        except Exception as e:
            logging.error(f"❌ Error creating looped video: {str(e)}")
            # Fallback: just copy the 10s video
            import shutil
            shutil.copy2(video_10s_path, output_path)
            logging.info(f"⚠️ Fallback: Using 10s video as-is")
            return output_path

    async def _generate_debug_video(self, image_path: str, audio_path: str,
                                  output_filename: Optional[str], animation_style: str) -> Dict[str, Any]:
        """Generate debug video using static image and audio (no Hedra API calls)

        Args:
            image_path: Path to the source image
            audio_path: Path to the audio file
            output_filename: Optional filename for output
            animation_style: Animation style

        Returns:
            Dict containing debug video result
        """
        try:
            from moviepy.editor import ImageClip, AudioFileClip

            # Generate filename if not provided
            if not output_filename:
                timestamp = int(time.time())
                output_filename = f"hedra_debug_video_{timestamp}.mp4"

            output_path = os.path.join(self.output_dir, output_filename)

            # Load audio to get duration
            audio_clip = AudioFileClip(audio_path)
            duration = audio_clip.duration

            # Create simple image clip with duration
            image_clip = ImageClip(image_path, duration=duration)

            # Resize to TikTok format (9:16)
            image_clip = image_clip.resize(height=1920).crop(width=1080, height=1920,
                                                           x_center=image_clip.w/2, y_center=image_clip.h/2)

            # Set audio
            final_video = image_clip.set_audio(audio_clip)

            # Write video
            final_video.write_videofile(
                output_path,
                fps=30,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile='temp-audio.m4a',
                remove_temp=True,
                verbose=False,
                logger=None
            )

            # Clean up
            audio_clip.close()
            image_clip.close()
            final_video.close()

            logging.info(f"🔧 DEBUG: Created static video: {output_path}")

            return {
                'path': output_path,
                'generation_id': 'debug_mode',
                'animation_style': animation_style,
                'animation_prompt': f'DEBUG: {animation_style} style (no animation)',
                'timestamp': time.time(),
                'provider': 'Debug Mode (Static)',
                'credit_optimization': 'debug_mode_no_credits_used'
            }

        except Exception as e:
            logging.error(f"❌ Error creating debug video: {str(e)}")
            raise

    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()
