# 🎙️ ElevenLabs Integration Guide

## Overview
Your TikTok automation app now uses **ElevenLabs** instead of AppyHub for high-quality text-to-speech generation. ElevenLabs provides superior voice quality, natural-sounding speech, and advanced voice cloning capabilities.

## ✅ What Changed

### **Files Updated:**
1. **`src/ai/audio_tools.py`** - Complete rewrite for ElevenLabs API
2. **`src/ai/__init__.py`** - Updated imports
3. **`src/core/pipeline.py`** - Updated to use ElevenLabsAudio
4. **`config.json`** - Added elevenlabs API key field
5. **`src/gui/settings_frame.py`** - Added ElevenLabs API key input

### **Class Renamed:**
- `AppyHubAudio` → `ElevenLabsAudio`

## 🔑 API Key Setup

### **1. Get ElevenLabs API Key:**
1. Go to https://elevenlabs.io/
2. Sign up for an account
3. Navigate to Profile → API Keys
4. Generate a new API key
5. Copy the key (starts with `sk-...`)

### **2. Add to Application:**
1. Open your TikTok automation app
2. Go to **Settings** tab
3. Find **"ElevenLabs API Key"** field
4. Paste your API key
5. Click **"Save Settings"**

## 🎵 Voice Options

### **Available Voices:**
- **Adam** (`male`, `male1`, `ai2`) - Deep, natural male voice
- **Bella** (`female`, `female1`) - Young, clear female voice  
- **Arnold** (`ai`, `male2`, `ai1`) - Crisp, professional voice
- **Domi** (`female2`) - Strong, confident female voice

### **Voice Quality:**
- **Model**: `eleven_monolingual_v1` (fast, high quality)
- **Stability**: 0.5 (balanced consistency)
- **Similarity Boost**: 0.75 (high voice similarity)
- **Speaker Boost**: Enabled (enhanced clarity)

## 💰 Pricing & Usage

### **ElevenLabs Pricing:**
- **Free Tier**: 10,000 characters/month
- **Starter**: $5/month for 30,000 characters  
- **Creator**: $22/month for 100,000 characters
- **Pro**: $99/month for 500,000 characters

### **Cost Estimation:**
- **Average TikTok story**: 200-500 characters
- **Cost per video**: $0.001-0.01 (Creator plan)
- **30 videos/month**: ~$0.30-3.00

### **Character Count Tool:**
The app includes a built-in character counter:
```python
cost_info = await audio_tools.get_character_count(story_text)
print(f"Characters: {cost_info['character_count']}")
print(f"Estimated cost: ${cost_info['estimated_cost_usd']}")
```

## 🚀 New Features

### **1. Voice Cloning** (Premium Feature)
```python
# Clone a custom voice
cloned_voice = await audio_tools.clone_voice(
    name="My Custom Voice",
    audio_files=["sample1.mp3", "sample2.mp3"],
    description="Professional narrator voice"
)
```

### **2. Available Voices Discovery**
```python
# Get all available voices
voices = await audio_tools.get_available_voices()
for voice in voices:
    print(f"{voice['name']}: {voice['description']}")
```

### **3. Multiple Models**
- `eleven_monolingual_v1` - Fast, good quality (default)
- `eleven_multilingual_v1` - Multiple languages
- `eleven_multilingual_v2` - Latest multilingual
- `eleven_turbo_v2` - Fastest generation

## 🔧 Technical Details

### **API Endpoint:**
```
https://api.elevenlabs.io/v1/text-to-speech/{voice_id}
```

### **Request Format:**
```json
{
  "text": "Your story text here",
  "model_id": "eleven_monolingual_v1",
  "voice_settings": {
    "stability": 0.5,
    "similarity_boost": 0.75,
    "style": 0.0,
    "use_speaker_boost": true
  }
}
```

### **Response:**
- Direct audio/mpeg stream
- No JSON wrapper (unlike AppyHub)
- Immediate download to local file

## 🛠️ Usage in Enhanced Workflow

### **Step 2: Audio Generation**
```python
# Generate speech with ElevenLabs
audio_path = await self.audio_tools.generate_speech(
    text=story_result['text'],
    voice_type=account['voice_type']  # 'male', 'female', 'ai', etc.
)
```

### **Voice Type Mapping:**
- `male` → Adam (Deep Male)
- `female` → Bella (Young Female)  
- `ai` → Arnold (Crisp AI)
- `male1` → Adam (Deep Male)
- `male2` → Arnold (Crisp Male)
- `female1` → Bella (Young Female)
- `female2` → Domi (Strong Female)
- `ai1` → Arnold (Crisp AI)
- `ai2` → Adam (Deep AI)

## 🔍 Testing

### **1. Test Basic Generation:**
1. Select an account in the app
2. Ensure "Enhanced AI" workflow is selected
3. Click "Generate Video"
4. Monitor logs for ElevenLabs API calls

### **2. Test Without API Key:**
- App should gracefully fall back to Legacy workflow
- No crashes or errors

### **3. Test Voice Types:**
- Create accounts with different voice types
- Verify correct voices are used

## 📊 Monitoring

### **Logs to Watch:**
```
INFO - Generating speech with ElevenLabs: 245 chars, voice: female
INFO - Speech generated successfully: /path/to/audio.mp3
```

### **Error Handling:**
- Invalid API key → Clear error message
- Quota exceeded → Graceful fallback
- Network issues → Retry logic

## 🆚 ElevenLabs vs AppyHub

| Feature | ElevenLabs | AppyHub |
|---------|------------|---------|
| **Voice Quality** | ⭐⭐⭐⭐⭐ Excellent | ⭐⭐⭐ Good |
| **Natural Sound** | ⭐⭐⭐⭐⭐ Very Natural | ⭐⭐⭐ Synthetic |
| **Voice Cloning** | ✅ Available | ❌ Not available |
| **Languages** | ✅ 29+ languages | ✅ Multiple |
| **Pricing** | 💰 $5-99/month | 💰 Pay per use |
| **Free Tier** | ✅ 10K chars/month | ❌ Limited |
| **API Stability** | ⭐⭐⭐⭐⭐ Excellent | ⭐⭐⭐ Good |

## 🚨 Troubleshooting

### **Common Issues:**

1. **"ElevenLabs API key not configured"**
   - Add API key in Settings → ElevenLabs API Key

2. **"HTTP 401: Unauthorized"**
   - Check API key is correct
   - Verify account is active

3. **"HTTP 429: Too Many Requests"**
   - You've exceeded rate limits
   - Wait or upgrade plan

4. **"Audio generation failed"**
   - Check internet connection
   - Verify text is not empty
   - Check character limits

### **Fallback Behavior:**
- Missing API key → Uses Legacy workflow (gTTS)
- API failure → Retries then falls back
- Network issues → Exponential backoff retry

## 🎯 Best Practices

1. **Monitor Usage**: Track character consumption
2. **Choose Right Plan**: Start with Starter ($5/month)
3. **Test Voices**: Try different voices for your niche
4. **Batch Generation**: Generate multiple videos efficiently
5. **Quality Settings**: Use default settings for best results

## 📈 Next Steps

1. **Get ElevenLabs API Key** and add to settings
2. **Test Enhanced Workflow** with different voice types
3. **Monitor Costs** and adjust usage as needed
4. **Explore Voice Cloning** for custom brand voices
5. **Consider Upgrading** plan based on usage

The ElevenLabs integration provides professional-grade audio that will significantly improve your TikTok video quality! 🎉
