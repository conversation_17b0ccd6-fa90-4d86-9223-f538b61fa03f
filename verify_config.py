#!/usr/bin/env python3
"""
Verify TikTok Automation Configuration
"""

import json
import os

def verify_config():
    """Verify that configuration is set up correctly for legacy workflow"""
    
    print("🔍 Verifying TikTok Automation Configuration...")
    print("=" * 50)
    
    # Load config
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
        print("✅ Config file loaded successfully")
    except Exception as e:
        print(f"❌ Error loading config: {e}")
        return False
    
    # Check providers
    tts_provider = config.get('tts', {}).get('provider', 'unknown')
    image_provider = config.get('image', {}).get('provider', 'unknown')
    
    print(f"\n📋 Current Configuration:")
    print(f"   TTS Provider: {tts_provider}")
    print(f"   Image Provider: {image_provider}")
    
    # Verify legacy workflow setup
    if tts_provider == 'gtts' and image_provider == 'stable_diffusion':
        print("\n✅ Configuration is set up for LEGACY WORKFLOW")
        print("   This will use:")
        print("   • OpenAI for story generation")
        print("   • gTTS for text-to-speech")
        print("   • Stable Diffusion for images")
        print("   • MoviePy for video assembly")
        print("\n🚀 Ready to test! Run: python main.py")
        return True
    
    elif tts_provider == 'apyhub' and image_provider == 'piapi_midjourney':
        print("\n⚠️  Configuration is set up for ENHANCED WORKFLOW")
        print("   This requires additional dependencies:")
        print("   • httpx")
        print("   • aiofiles")
        print("\n🔧 To use enhanced workflow, run: pip install httpx aiofiles")
        print("🔧 Or switch to legacy workflow in Settings")
        return False
    
    else:
        print(f"\n❌ Mixed configuration detected:")
        print(f"   TTS: {tts_provider}")
        print(f"   Image: {image_provider}")
        print("\n🔧 Please check your settings")
        return False

def check_directories():
    """Check if required directories exist"""
    print("\n📁 Checking directories...")
    
    dirs_to_check = [
        'data',
        'data/audio',
        'data/images', 
        'data/videos',
        'output'
    ]
    
    for dir_path in dirs_to_check:
        if os.path.exists(dir_path):
            print(f"   ✅ {dir_path}")
        else:
            print(f"   📁 Creating {dir_path}")
            os.makedirs(dir_path, exist_ok=True)

def check_api_keys():
    """Check if required API keys are configured"""
    print("\n🔑 Checking API keys...")
    
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        api_keys = config.get('api_keys', {})
        openai_key = api_keys.get('openai', '')
        
        if openai_key and openai_key.startswith('sk-'):
            print("   ✅ OpenAI API key configured")
        else:
            print("   ⚠️  OpenAI API key missing or invalid")
            
        # For legacy workflow, only OpenAI is required
        print("   ℹ️  Legacy workflow only requires OpenAI API key")
        
    except Exception as e:
        print(f"   ❌ Error checking API keys: {e}")

if __name__ == "__main__":
    print("🎬 TikTok Automation Configuration Checker")
    print("=" * 50)
    
    # Run checks
    config_ok = verify_config()
    check_directories()
    check_api_keys()
    
    print("\n" + "=" * 50)
    if config_ok:
        print("🎉 READY TO TEST!")
        print("\n📝 Test with this data:")
        print("   Account: test_horror_account")
        print("   Niche: horror")
        print("   Theme: Scary Stories")
        print("   Topic: A person finds an old diary that writes itself")
        print("   Voice: female")
        print("\n🚀 Run: python main.py")
    else:
        print("⚠️  Configuration needs adjustment")
        print("🔧 Please fix the issues above before testing")
    
    input("\nPress Enter to continue...")
