#!/usr/bin/env python3
"""
Test script to verify Enhanced AI workflow imports and initialization
"""

import os
import sys
import logging
import json

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_dependencies():
    """Test if all required dependencies are available"""
    print("🔍 Testing dependencies...")
    
    try:
        import httpx
        print("✅ httpx imported successfully")
    except ImportError as e:
        print(f"❌ httpx import failed: {e}")
        return False
    
    try:
        import aiofiles
        print("✅ aiofiles imported successfully")
    except ImportError as e:
        print(f"❌ aiofiles import failed: {e}")
        return False
    
    try:
        import asyncio_throttle
        print("✅ asyncio_throttle imported successfully")
    except ImportError as e:
        print(f"❌ asyncio_throttle import failed: {e}")
        return False
    
    return True

def test_enhanced_imports():
    """Test if Enhanced AI workflow components can be imported"""
    print("\n🔍 Testing Enhanced AI workflow imports...")
    
    try:
        from src.ai.story_generator import EnhancedStoryGenerator
        print("✅ EnhancedStoryGenerator imported successfully")
    except ImportError as e:
        print(f"❌ EnhancedStoryGenerator import failed: {e}")
        return False
    
    try:
        from src.ai.image_tools import PiAPIMidjourneyTools
        print("✅ PiAPIMidjourneyTools imported successfully")
    except ImportError as e:
        print(f"❌ PiAPIMidjourneyTools import failed: {e}")
        return False
    
    try:
        from src.ai.audio_tools import ApyHubAudio
        print("✅ ApyHubAudio imported successfully")
    except ImportError as e:
        print(f"❌ ApyHubAudio import failed: {e}")
        return False
    
    try:
        from src.ai.enhanced_video_assembler import EnhancedVideoAssembler
        print("✅ EnhancedVideoAssembler imported successfully")
    except ImportError as e:
        print(f"❌ EnhancedVideoAssembler import failed: {e}")
        return False
    
    try:
        from src.core.pipeline import VideoGenerationPipeline
        print("✅ VideoGenerationPipeline imported successfully")
    except ImportError as e:
        print(f"❌ VideoGenerationPipeline import failed: {e}")
        return False
    
    try:
        from src.core.account_manager import EnhancedAccountManager
        print("✅ EnhancedAccountManager imported successfully")
    except ImportError as e:
        print(f"❌ EnhancedAccountManager import failed: {e}")
        return False
    
    try:
        from src.core.scheduler import AutoGenerationScheduler
        print("✅ AutoGenerationScheduler imported successfully")
    except ImportError as e:
        print(f"❌ AutoGenerationScheduler import failed: {e}")
        return False
    
    return True

def test_config_loading():
    """Test if configuration can be loaded"""
    print("\n🔍 Testing configuration loading...")
    
    try:
        config_path = "config.json"
        if not os.path.exists(config_path):
            print(f"❌ Config file not found: {config_path}")
            return False
        
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        print("✅ Configuration loaded successfully")
        
        # Check API keys
        api_keys = config.get('api_keys', {})
        required_keys = ['openai', 'piapi', 'apyhub', 'hedra']
        missing_keys = []
        
        for key in required_keys:
            if not api_keys.get(key, '').strip():
                missing_keys.append(key)
        
        if missing_keys:
            print(f"⚠️  Missing API keys: {missing_keys}")
        else:
            print("✅ All required API keys are configured")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration loading failed: {e}")
        return False

def test_component_initialization():
    """Test if Enhanced AI workflow components can be initialized"""
    print("\n🔍 Testing component initialization...")
    
    try:
        # Load config
        with open("config.json", 'r') as f:
            config = json.load(f)
        
        # Test story generator
        from src.ai.story_generator import EnhancedStoryGenerator
        story_gen = EnhancedStoryGenerator(config)
        print("✅ EnhancedStoryGenerator initialized successfully")
        
        # Test image tools
        from src.ai.image_tools import PiAPIMidjourneyTools
        image_tools = PiAPIMidjourneyTools(config)
        print("✅ PiAPIMidjourneyTools initialized successfully")
        
        # Test audio tools
        from src.ai.audio_tools import ApyHubAudio
        audio_tools = ApyHubAudio(config)
        print("✅ ApyHubAudio initialized successfully")
        
        # Test video assembler
        from src.ai.enhanced_video_assembler import EnhancedVideoAssembler
        video_assembler = EnhancedVideoAssembler(config)
        print("✅ EnhancedVideoAssembler initialized successfully")
        
        # Test database manager (needed for pipeline)
        from src.database.db_manager import DatabaseManager
        db_path = os.path.join("data", "tiktok_automation.db")
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        db_manager = DatabaseManager(db_path)
        db_manager.initialize_database()
        print("✅ DatabaseManager initialized successfully")
        
        # Test pipeline
        from src.core.pipeline import VideoGenerationPipeline
        pipeline = VideoGenerationPipeline(config, db_manager)
        print("✅ VideoGenerationPipeline initialized successfully")
        
        # Test account manager
        from src.core.account_manager import EnhancedAccountManager
        account_manager = EnhancedAccountManager(db_manager, config)
        print("✅ EnhancedAccountManager initialized successfully")
        
        # Test scheduler
        from src.core.scheduler import AutoGenerationScheduler
        scheduler = AutoGenerationScheduler(config, db_manager, pipeline, account_manager)
        print("✅ AutoGenerationScheduler initialized successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Component initialization failed: {e}")
        logging.error(f"Component initialization error: {e}", exc_info=True)
        return False

def main():
    """Main test function"""
    print("🚀 Enhanced AI Workflow Test Suite")
    print("=" * 50)
    
    all_tests_passed = True
    
    # Test 1: Dependencies
    if not test_dependencies():
        all_tests_passed = False
    
    # Test 2: Imports
    if not test_enhanced_imports():
        all_tests_passed = False
    
    # Test 3: Configuration
    if not test_config_loading():
        all_tests_passed = False
    
    # Test 4: Component initialization
    if not test_component_initialization():
        all_tests_passed = False
    
    print("\n" + "=" * 50)
    if all_tests_passed:
        print("🎉 All tests passed! Enhanced AI workflow is ready.")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    
    return all_tests_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
