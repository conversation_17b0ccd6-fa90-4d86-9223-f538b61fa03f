# 🔑 Complete API Guide for TikTok Automation

## 📋 **API Overview**

Your TikTok automation app uses several APIs to create high-quality videos. Here's what each one does and where to get them:

---

## ✅ **REQUIRED APIs** (Essential for Enhanced Workflow)

### 1. 🧠 **OpenAI API** 
**Status**: ✅ **Already Configured**

- **Purpose**: Story generation and content optimization
- **Where**: https://platform.openai.com/
- **Cost**: ~$0.01-0.05 per video story
- **Your Key**: Already set up ✅

### 2. 🎙️ **ElevenLabs API**
**Status**: ✅ **Already Configured**

- **Purpose**: High-quality text-to-speech
- **Where**: https://elevenlabs.io/
- **Cost**: Free tier (10K chars/month), then $5-22/month
- **Your Key**: Already set up ✅

### 3. 📸 **ImgBB API**
**Status**: ✅ **Already Configured**

- **Purpose**: Temporary image hosting for API processing
- **Where**: https://imgbb.com/api
- **Cost**: **FREE** (100 uploads/hour)
- **Your Key**: Already set up ✅

---

## 🎨 **OPTIONAL APIs** (For Enhanced Image Generation)

### 4. **Replicate API** (Recommended)
**Status**: ⚠️ **Optional - Not Required**

- **Purpose**: High-quality AI image generation (SDXL, Midjourney-style)
- **Where**: https://replicate.com/
- **Cost**: Pay-per-use (~$0.01-0.05 per image)
- **How to get**:
  1. Go to https://replicate.com/
  2. Sign up for account
  3. Go to Account → API tokens
  4. Create new token
  5. Add to app Settings

### 5. **Stability AI API** (Alternative)
**Status**: ⚠️ **Optional - Alternative to Replicate**

- **Purpose**: Stable Diffusion image generation
- **Where**: https://platform.stability.ai/
- **Cost**: Credit-based (~$0.04 per image, $10 minimum)
- **How to get**:
  1. Go to https://platform.stability.ai/
  2. Sign up for account
  3. Go to API Keys section
  4. Generate new API key
  5. Add to app Settings

---

## 🎬 **VIDEO ASSEMBLY APIs** (Advanced Features)

### 6. **Hedra API**
**Status**: ⚠️ **Optional - For Animated Videos**

- **Purpose**: Lip-sync animation and talking head videos
- **Where**: https://hedra.com/
- **Cost**: ~$0.10-0.50 per video
- **Note**: May require waitlist access

---

## ❌ **REMOVED/UNAVAILABLE APIs**

### ~~Pi.ai API~~ ❌ **NOT AVAILABLE**
- **Status**: Removed from settings
- **Reason**: Pi.ai doesn't have a public API
- **Replacement**: Use Replicate or Stability AI instead

### ~~AppyHub API~~ ❌ **REPLACED**
- **Status**: Replaced with ElevenLabs
- **Reason**: ElevenLabs provides better voice quality
- **Migration**: Complete ✅

---

## 🎯 **Recommended Setup for Different Budgets**

### 💰 **FREE Tier** (Perfect for Testing)
```
✅ OpenAI (already have)
✅ ElevenLabs (10K chars/month free)
✅ ImgBB (free image hosting)
❌ Skip image generation APIs
```
**Monthly Cost**: $0 (using existing OpenAI credits)

### 💵 **Budget Setup** ($10-15/month)
```
✅ OpenAI (already have)
✅ ElevenLabs Starter ($5/month)
✅ ImgBB (free)
✅ Replicate ($5-10 credit for testing)
❌ Skip Hedra for now
```
**Monthly Cost**: ~$10-15

### 💎 **Professional Setup** ($25-50/month)
```
✅ OpenAI (already have)
✅ ElevenLabs Creator ($22/month)
✅ ImgBB (free)
✅ Replicate (pay-per-use)
✅ Hedra (for animated videos)
```
**Monthly Cost**: ~$25-50

---

## 🔧 **How to Add API Keys**

### **Step-by-Step:**
1. Open your TikTok automation app
2. Go to **Settings** tab
3. Scroll to **API Keys** section
4. Find the API you want to configure
5. Click the **"?"** button for detailed instructions
6. Paste your API key in the field
7. Click **"Save Settings"**

### **API Key Format Examples:**
- **OpenAI**: `sk-proj-...` (already configured)
- **ElevenLabs**: `sk_...` (already configured)
- **Replicate**: `r8_...`
- **Stability**: `sk-...`
- **ImgBB**: `47375c636...` (already configured)

---

## 🚀 **Testing Your Setup**

### **1. Test Basic Workflow** (Free)
- Use **Legacy** workflow mode
- Generate a video with existing APIs
- Should work with OpenAI + gTTS + basic images

### **2. Test Enhanced Workflow** (With APIs)
- Switch to **Enhanced AI** workflow mode
- Generate a video with ElevenLabs audio
- Add image generation APIs for better visuals

### **3. Monitor Usage**
- Check API dashboards for usage
- Monitor costs in each service
- Adjust plans as needed

---

## 📊 **Cost Breakdown Per Video**

| Component | Free Tier | Budget | Professional |
|-----------|-----------|---------|--------------|
| **Story (OpenAI)** | $0.01 | $0.01 | $0.01 |
| **Audio (ElevenLabs)** | Free* | $0.01 | $0.01 |
| **Image (Optional)** | $0 | $0.03 | $0.03 |
| **Video Assembly** | $0 | $0 | $0.25 |
| **Total per video** | **$0.01** | **$0.05** | **$0.30** |

*Free tier: 10,000 characters/month

---

## 🆘 **Troubleshooting**

### **"API key not configured"**
- Add the API key in Settings
- Make sure it's the correct format
- Check if account is active

### **"Enhanced workflow not available"**
- At least OpenAI + ElevenLabs + ImgBB needed
- Check all required APIs are configured
- Try Legacy workflow as fallback

### **High costs**
- Monitor usage in API dashboards
- Use free tiers when possible
- Consider lower-cost alternatives

---

## 🎯 **Quick Start Recommendation**

### **For Immediate Testing:**
1. ✅ **Use what you have**: OpenAI + ElevenLabs + ImgBB (all configured)
2. ✅ **Test Enhanced workflow**: Generate a video to see quality
3. ✅ **Monitor results**: Check if quality improvement is worth it

### **For Production Use:**
1. 🎨 **Add Replicate**: $5 credit for high-quality images
2. 📊 **Monitor usage**: Track costs for 1 week
3. 📈 **Scale up**: Add more APIs based on results

Your app is already configured with the essential APIs for high-quality video generation! 🎉
