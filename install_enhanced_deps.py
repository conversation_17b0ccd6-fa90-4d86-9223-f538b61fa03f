#!/usr/bin/env python3
"""
Install Enhanced AI Workflow Dependencies
"""

import subprocess
import sys
import os

def install_dependencies():
    """Install required dependencies for enhanced AI workflow"""
    
    print("🚀 Installing Enhanced AI Workflow Dependencies...")
    print("=" * 50)
    
    # List of required packages
    packages = [
        "httpx>=0.24.0",
        "aiofiles>=23.0.0"
    ]
    
    for package in packages:
        print(f"📦 Installing {package}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ Successfully installed {package}")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package}: {e}")
            return False
    
    print("\n🎉 All dependencies installed successfully!")
    print("\nYou can now use the Enhanced AI workflow with:")
    print("• OpenAI (GPT-4) for story generation")
    print("• ApyHub for text-to-audio conversion")
    print("• PiAPI (Midjourney) for image generation")
    print("• He<PERSON> for video assembly")
    
    return True

if __name__ == "__main__":
    success = install_dependencies()
    if success:
        print("\n✨ Ready to generate amazing TikTok videos!")
    else:
        print("\n⚠️  Some dependencies failed to install. Please check the errors above.")
    
    input("\nPress Enter to continue...")
