# 🎬 TikTok Automation - Enhanced AI Edition

A powerful desktop application for automated TikTok video generation using cutting-edge AI tools and workflows.

## ✨ Features

### 🚀 Enhanced AI Workflow
- **Viral Story Generation**: OpenAI GPT with optimized prompts for viral content
- **Image Analysis**: Midjourney Describe API for detailed image understanding
- **Advanced Image Generation**: Midjourney Text-to-Image via pi.ai API
- **Professional Audio**: AppyHub TTS with multiple voice options
- **Animated Videos**: Hedra API for lip-sync and animation
- **Viral Score Calculation**: AI-powered content optimization scoring

### 🔧 Legacy Workflow (Fallback)
- Basic story generation with OpenAI
- Simple image generation (Stable Diffusion/Craiyon)
- gTTS for speech synthesis
- MoviePy for video assembly

### 📊 Account Management
- Multiple TikTok account profiles
- Niche-specific content generation
- Voice type customization per account
- Daily video limits and scheduling
- Performance tracking and analytics

### ⚡ Automation Features
- **Auto-Scheduler**: Automated video generation during optimal hours
- **Batch Generation**: Process multiple accounts concurrently
- **Smart Retry Logic**: Robust error handling and fallback systems
- **Performance Analytics**: Track success rates and viral scores

## 🛠️ Installation

### Prerequisites
- Python 3.8+
- Windows OS (for .exe distribution)

### Setup
1. Clone the repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Configure API keys in the Settings tab
4. Run the application:
   ```bash
   python main.py
   ```

### Building Executable
```bash
python build_exe.py
```

## 🔑 Required API Keys

### Enhanced AI Workflow
- **OpenAI**: Story generation and prompt optimization
- **Pi.ai**: Midjourney Describe and Image Generation
- **AppyHub**: High-quality text-to-speech
- **Hedra**: Animated video assembly
- **ImgBB**: Temporary image hosting

### Legacy Workflow
- **OpenAI**: Basic story generation (optional)
- **Stable Diffusion**: Image generation (optional)

## 📋 Usage

### Quick Start
1. **Add Account**: Create TikTok account profiles with niche and preferences
2. **Configure APIs**: Enter your API keys in Settings > API Keys
3. **Select Workflow**: Choose Enhanced AI or Legacy mode
4. **Generate Video**: Select account and click "Generate Video"
5. **Export**: Review and export videos for manual upload

### Enhanced Workflow Process
1. 🧠 **Story Generation**: AI creates viral-optimized story based on niche
2. 🔊 **Audio Creation**: Professional TTS with selected voice type
3. 🖼️ **Base Image**: Generate or select starting image
4. ☁️ **Image Upload**: Temporary hosting for API processing
5. 🔍 **Image Analysis**: Midjourney Describe extracts visual elements
6. ✨ **Prompt Refinement**: GPT optimizes image generation prompt
7. 🎨 **Enhanced Image**: Midjourney generates photorealistic image
8. 🎬 **Video Assembly**: Hedra creates animated lip-sync video
9. 💾 **Save & Export**: Store in account folder for manual upload

### Auto-Scheduler
- Set generation windows (morning, afternoon, evening)
- Automatic account prioritization
- Concurrent generation limits
- Performance tracking

## 📁 Project Structure

```
src/
├── ai/                     # Enhanced AI workflow
│   ├── story_generator.py  # Viral story generation
│   ├── image_tools.py      # Midjourney integration
│   ├── audio_tools.py      # AppyHub audio
│   ├── video_assembler.py  # Hedra video assembly
│   └── image_hosting.py    # Temporary image hosting
├── core/                   # Core functionality
│   ├── pipeline.py         # Main generation pipeline
│   ├── account_manager.py  # Enhanced account management
│   └── scheduler.py        # Auto-generation scheduler
├── gui/                    # User interface
├── database/              # SQLite database
└── utils/                 # Utilities and config
```

## ⚙️ Configuration

### Workflow Settings
- **Enhanced AI**: Full AI pipeline with all APIs
- **Legacy**: Basic generation with minimal dependencies
- **Hybrid**: Mix of enhanced and legacy components

### Generation Settings
- Video duration: 15-180 seconds
- Voice types: Male, Female, AI variants
- Quality settings: High, Medium, Low
- Aspect ratio: 9:16 (TikTok optimized)

### Scheduler Settings
- Check interval: 1-24 hours
- Generation windows: Customizable time slots
- Concurrent limit: 1-5 simultaneous generations
- Account prioritization: Performance-based

## 📊 Analytics & Monitoring

### Performance Metrics
- Generation success rates
- Average viral scores
- Account utilization
- Error tracking and logging

### Viral Score Factors
- Hook effectiveness (first 3 seconds)
- Engagement triggers
- Niche optimization
- Content structure quality

## 🚨 Error Handling

### Robust Fallbacks
- API failures → Alternative providers
- Enhanced workflow issues → Legacy fallback
- Network problems → Retry with exponential backoff
- File corruption → Regeneration attempts

### Logging
- Comprehensive error logging
- Performance monitoring
- API usage tracking
- Generation history

## 🔒 Security & Privacy

- API keys stored locally in encrypted config
- No data sent to external servers (except APIs)
- Local file storage and processing
- Manual upload requirement (no auto-posting)

## 📈 Optimization Tips

### For Best Results
1. Use specific, engaging niches
2. Configure optimal voice types per account
3. Set realistic daily limits
4. Monitor viral scores and adjust
5. Use Enhanced AI workflow for quality

### Performance Tuning
- Adjust concurrent generation limits
- Optimize generation windows
- Monitor API quotas and limits
- Regular cleanup of temporary files

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Implement changes with tests
4. Submit pull request

## 📄 License

This project is licensed under the MIT License.

## ⚠️ Disclaimer

This tool is for content creation assistance only. Users are responsible for:
- Compliance with TikTok's terms of service
- Content originality and copyright
- Manual review before posting
- API usage costs and limits

## 🆘 Support

For issues and questions:
1. Check the logs in the Logs tab
2. Verify API key configuration
3. Test with Legacy workflow first
4. Review error messages and documentation
