#!/usr/bin/env python3
"""
Install Better Audio Dependencies for TikTok Automation
"""

import subprocess
import sys
import os

def install_dependencies():
    """Install required dependencies for better audio quality"""
    
    print("🎙️ Installing Better Audio Dependencies...")
    print("=" * 50)
    
    # List of required packages
    packages = [
        "pyttsx3>=2.90",  # Better TTS engine
        "pydub>=0.25.1"   # Audio conversion
    ]
    
    for package in packages:
        print(f"📦 Installing {package}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ Successfully installed {package}")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package}: {e}")
            return False
    
    print("\n🎉 All audio dependencies installed successfully!")
    print("\nYour TikTok automation now has:")
    print("• 🎙️ Better quality human-like voices (pyttsx3)")
    print("• 🎨 Beautiful scenic background images")
    print("• 🌲 Forest, mountain, and nature scenes")
    print("• 🎬 Proper video assembly")
    
    return True

def test_audio():
    """Test the audio system"""
    print("\n🧪 Testing audio system...")
    
    try:
        import pyttsx3
        engine = pyttsx3.init()
        voices = engine.getProperty('voices')
        print(f"✅ Found {len(voices)} system voices")
        
        for i, voice in enumerate(voices[:3]):  # Show first 3 voices
            print(f"   Voice {i+1}: {voice.name}")
            
    except Exception as e:
        print(f"⚠️ Audio test failed: {e}")
        print("   Falling back to gTTS (Google TTS)")

if __name__ == "__main__":
    print("🎬 TikTok Automation - Better Audio Setup")
    print("=" * 50)
    
    success = install_dependencies()
    if success:
        test_audio()
        print("\n✨ Ready to generate amazing TikTok videos with better audio!")
        print("\n🚀 Run: python main.py")
    else:
        print("\n⚠️  Some dependencies failed to install. The app will use fallback options.")
    
    input("\nPress Enter to continue...")
