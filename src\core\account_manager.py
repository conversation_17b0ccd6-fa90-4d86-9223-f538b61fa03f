"""
Enhanced Account Manager for TikTok Automation
Advanced account management with generation tracking and optimization
"""

import os
import logging
import time
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import json

class EnhancedAccountManager:
    """Enhanced account manager with advanced features"""
    
    def __init__(self, db_manager, config: Dict[str, Any]):
        """Initialize enhanced account manager
        
        Args:
            db_manager: Database manager instance
            config: Configuration dictionary
        """
        self.db_manager = db_manager
        self.config = config
        
        # Account performance tracking
        self.performance_cache = {}
        self.generation_limits = {}
        
        # Output directory management
        self.base_output_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "output")
        os.makedirs(self.base_output_dir, exist_ok=True)
    
    def create_account_with_validation(self, account_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create account with enhanced validation and setup
        
        Args:
            account_data: Account data to validate and create
            
        Returns:
            Dict: Created account with additional metadata
        """
        # Validate required fields
        required_fields = ['username', 'niche', 'video_duration', 'max_videos_per_day', 'voice_type']
        for field in required_fields:
            if field not in account_data:
                raise ValueError(f"Missing required field: {field}")
        
        # Validate field values
        self._validate_account_data(account_data)
        
        # Create account in database
        account_id = self.db_manager.add_account(account_data)
        
        # Create account output directory
        account_dir = os.path.join(self.base_output_dir, account_data['username'])
        os.makedirs(account_dir, exist_ok=True)
        
        # Initialize performance tracking
        self.performance_cache[account_id] = {
            'total_videos': 0,
            'successful_generations': 0,
            'failed_generations': 0,
            'avg_viral_score': 0,
            'last_generation': None,
            'daily_count': 0,
            'last_reset_date': datetime.now().date()
        }
        
        # Get complete account data
        account = self.db_manager.get_account(account_id)
        
        logging.info(f"Created enhanced account: {account_data['username']} (ID: {account_id})")
        return account
    
    def _validate_account_data(self, account_data: Dict[str, Any]):
        """Validate account data
        
        Args:
            account_data: Account data to validate
        """
        # Validate username
        username = account_data['username'].strip()
        if not username or len(username) < 3:
            raise ValueError("Username must be at least 3 characters long")
        
        # Validate niche
        valid_niches = ['horror', 'mystery', 'haunted house', 'true crime', 'paranormal', 
                       'comedy', 'lifestyle', 'educational', 'entertainment']
        if account_data['niche'].lower() not in valid_niches:
            logging.warning(f"Niche '{account_data['niche']}' not in predefined list, but allowing custom niches")
        
        # Validate video duration
        duration = account_data['video_duration']
        if not isinstance(duration, int) or duration < 15 or duration > 180:
            raise ValueError("Video duration must be between 15 and 180 seconds")
        
        # Validate max videos per day
        max_videos = account_data['max_videos_per_day']
        if not isinstance(max_videos, int) or max_videos < 1 or max_videos > 20:
            raise ValueError("Max videos per day must be between 1 and 20")
        
        # Validate voice type
        valid_voices = ['male', 'female', 'ai', 'male1', 'male2', 'female1', 'female2', 'ai1', 'ai2']
        if account_data['voice_type'] not in valid_voices:
            raise ValueError(f"Voice type must be one of: {', '.join(valid_voices)}")
    
    def get_accounts_for_generation(self, active_only: bool = True) -> List[Dict[str, Any]]:
        """Get accounts ready for video generation
        
        Args:
            active_only: Only return active accounts
            
        Returns:
            List: Accounts ready for generation
        """
        accounts = self.db_manager.get_all_accounts(active_only=active_only)
        ready_accounts = []
        
        for account in accounts:
            if self.can_generate_video(account):
                # Add performance data
                account['performance'] = self.get_account_performance(account['id'])
                ready_accounts.append(account)
        
        return ready_accounts
    
    def can_generate_video(self, account: Dict[str, Any]) -> bool:
        """Check if account can generate a new video
        
        Args:
            account: Account to check
            
        Returns:
            bool: True if can generate
        """
        account_id = account['id']
        
        # Check if account is active
        if account.get('status') != 'active':
            return False
        
        # Update daily count if needed
        self._update_daily_count(account_id)
        
        # Check daily limit
        performance = self.performance_cache.get(account_id, {})
        daily_count = performance.get('daily_count', 0)
        
        if daily_count >= account['max_videos_per_day']:
            return False
        
        # Check minimum time between generations (optional cooldown)
        last_generation = performance.get('last_generation')
        if last_generation:
            time_since_last = time.time() - last_generation
            min_interval = 3600  # 1 hour minimum between generations
            if time_since_last < min_interval:
                return False
        
        return True
    
    def _update_daily_count(self, account_id: int):
        """Update daily generation count for account
        
        Args:
            account_id: Account ID
        """
        if account_id not in self.performance_cache:
            self.performance_cache[account_id] = {
                'daily_count': 0,
                'last_reset_date': datetime.now().date()
            }
        
        performance = self.performance_cache[account_id]
        current_date = datetime.now().date()
        
        # Reset daily count if it's a new day
        if performance.get('last_reset_date') != current_date:
            performance['daily_count'] = 0
            performance['last_reset_date'] = current_date
    
    def record_generation_attempt(self, account_id: int, success: bool, 
                                viral_score: float = 0, error: str = None):
        """Record a video generation attempt
        
        Args:
            account_id: Account ID
            success: Whether generation was successful
            viral_score: Viral score of generated content
            error: Error message if failed
        """
        if account_id not in self.performance_cache:
            self.performance_cache[account_id] = {
                'total_videos': 0,
                'successful_generations': 0,
                'failed_generations': 0,
                'avg_viral_score': 0,
                'daily_count': 0,
                'last_reset_date': datetime.now().date()
            }
        
        performance = self.performance_cache[account_id]
        
        # Update counts
        performance['total_videos'] += 1
        performance['daily_count'] += 1
        performance['last_generation'] = time.time()
        
        if success:
            performance['successful_generations'] += 1
            
            # Update average viral score
            current_avg = performance.get('avg_viral_score', 0)
            successful_count = performance['successful_generations']
            performance['avg_viral_score'] = ((current_avg * (successful_count - 1)) + viral_score) / successful_count
        else:
            performance['failed_generations'] += 1
            if error:
                logging.error(f"Generation failed for account {account_id}: {error}")
    
    def get_account_performance(self, account_id: int) -> Dict[str, Any]:
        """Get performance metrics for an account
        
        Args:
            account_id: Account ID
            
        Returns:
            Dict: Performance metrics
        """
        if account_id not in self.performance_cache:
            return {
                'total_videos': 0,
                'successful_generations': 0,
                'failed_generations': 0,
                'success_rate': 0,
                'avg_viral_score': 0,
                'daily_count': 0,
                'can_generate_today': True
            }
        
        performance = self.performance_cache[account_id]
        total = performance['total_videos']
        successful = performance['successful_generations']
        
        return {
            'total_videos': total,
            'successful_generations': successful,
            'failed_generations': performance['failed_generations'],
            'success_rate': (successful / total * 100) if total > 0 else 0,
            'avg_viral_score': performance.get('avg_viral_score', 0),
            'daily_count': performance.get('daily_count', 0),
            'can_generate_today': True  # Will be calculated by can_generate_video
        }
    
    def get_top_performing_accounts(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get top performing accounts by viral score
        
        Args:
            limit: Maximum number of accounts to return
            
        Returns:
            List: Top performing accounts
        """
        accounts = self.db_manager.get_all_accounts(active_only=True)
        
        # Add performance data and sort
        for account in accounts:
            account['performance'] = self.get_account_performance(account['id'])
        
        # Sort by average viral score
        accounts.sort(key=lambda x: x['performance']['avg_viral_score'], reverse=True)
        
        return accounts[:limit]
    
    def optimize_account_settings(self, account_id: int) -> Dict[str, Any]:
        """Suggest optimizations for account settings based on performance
        
        Args:
            account_id: Account ID
            
        Returns:
            Dict: Optimization suggestions
        """
        account = self.db_manager.get_account(account_id)
        performance = self.get_account_performance(account_id)
        
        suggestions = {
            'current_performance': performance,
            'suggestions': []
        }
        
        # Analyze performance and suggest improvements
        success_rate = performance['success_rate']
        avg_viral_score = performance['avg_viral_score']
        
        if success_rate < 70:
            suggestions['suggestions'].append({
                'type': 'success_rate',
                'message': 'Low success rate detected. Consider adjusting niche or voice type.',
                'priority': 'high'
            })
        
        if avg_viral_score < 60:
            suggestions['suggestions'].append({
                'type': 'viral_score',
                'message': 'Low viral scores. Consider more engaging niches or shorter videos.',
                'priority': 'medium'
            })
        
        if performance['daily_count'] < account['max_videos_per_day'] * 0.5:
            suggestions['suggestions'].append({
                'type': 'utilization',
                'message': 'Account underutilized. Consider increasing generation frequency.',
                'priority': 'low'
            })
        
        return suggestions
    
    def export_account_data(self, account_id: int) -> Dict[str, Any]:
        """Export complete account data including performance
        
        Args:
            account_id: Account ID
            
        Returns:
            Dict: Complete account data
        """
        account = self.db_manager.get_account(account_id)
        if not account:
            raise ValueError(f"Account not found: {account_id}")
        
        # Get videos
        videos = self.db_manager.get_account_videos(account_id)
        
        # Get performance
        performance = self.get_account_performance(account_id)
        
        return {
            'account': account,
            'performance': performance,
            'videos': videos,
            'export_timestamp': time.time()
        }
    
    def cleanup_old_performance_data(self, days_to_keep: int = 30):
        """Clean up old performance data
        
        Args:
            days_to_keep: Number of days of data to keep
        """
        # This would implement cleanup of old performance tracking data
        # For now, just log the action
        logging.info(f"Performance data cleanup requested (keep {days_to_keep} days)")
        
        # In a full implementation, this would clean up old entries
        # from performance_cache based on timestamps
