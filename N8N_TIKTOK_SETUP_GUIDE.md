# 🎬 TikTok Automation n8n Template Setup Guide

## 📋 **Overview**

This n8n template creates an automated TikTok scary story generation workflow based on your existing Python automation but running in n8n cloud/self-hosted environment.

## 🔧 **Template File**
- **File**: `tiktok_automation_n8n_template.json`
- **Based on**: `animated-pod-n8n-template.json` (modified for TikTok)

## 🎯 **Workflow Steps**

1. **📅 Schedule Trigger** → Runs automatically (every minute/hour/day)
2. **🤖 OpenAI Story Generation** → Creates 60-second scary stories
3. **🎵 ApyHub Text-to-Speech** → Converts text to human-like audio
4. **⏳ Wait for Audio** → Allows processing time
5. **🎨 PiAPI Base Image** → Generates atmospheric scary images
6. **⏳ Wait for Image** → Allows processing time
7. **📤 ImgBB Upload** → Hosts image publicly
8. **🎭 OpenAI Visual Prompt** → Creates animation prompts
9. **🎬 Hedra Video Generation** → Creates 10-second animated videos
10. **⏳ Wait for Video** → Allows processing time
11. **☁️ Google Drive Upload** → Saves final video

## 🔑 **API Keys Required**

### **From your `api_keys.txt`:**
```
OpenAI: sk-proj-xxx (GPT-4 for stories & prompts)
ApyHub: APY0jyr56nnuCm5cazBjuYIVTOn1FyiFZAitdVzBxtGoVCek73LHlyynZk9ZZ2ll5DAeh6XNf9Mwj
PiAPI: 6d9c3e22532462701f580ce41a4e03100037876de129c4d8d359c6c18da3996c
Hedra: hd_sk_b8f7e6d5c4a3b2e1f9g8h7i6j5k4l3m2n1o0p9q8r7s6t5u4v3w2x1y0z
ImgBB: c9a3ba8c4f8c8f8e8e8e8e8e8e8e8e8e
```

### **Additional Required:**
- **Google Drive API** (for final upload)

## 🚀 **Setup Instructions**

### **Step 1: Import Template**
1. Open n8n (cloud or self-hosted)
2. Go to **Workflows** → **Import from file**
3. Upload `tiktok_automation_n8n_template.json`

### **Step 2: Configure Credentials**
1. **OpenAI API**:
   - Create credential: OpenAI API
   - API Key: `sk-proj-xxx` (from your api_keys.txt)

2. **Google Drive API**:
   - Create credential: Google Drive OAuth2 API
   - Follow OAuth setup for Google Drive access

### **Step 3: Update API Keys**
Replace placeholder keys in HTTP Request nodes:

1. **ApyHub Node**: Update `apy-token` header
2. **PiAPI Node**: Update `x-api-key` header  
3. **Hedra Node**: Update `Authorization` Bearer token
4. **ImgBB Node**: Update `key` parameter

### **Step 4: Configure Google Drive**
1. **Google Drive Upload Node**:
   - Set target folder ID
   - Configure file naming pattern
   - Test upload permissions

### **Step 5: Test Workflow**
1. **Manual Test**: Click "Execute Workflow"
2. **Check Each Step**: Verify API responses
3. **Monitor Timing**: Adjust wait times if needed

## ⚙️ **Key Differences from Python Version**

### **✅ Advantages:**
- **Cloud-based**: Runs without local machine
- **Scheduled**: Automatic execution
- **Visual**: Easy to modify workflow
- **Scalable**: Can handle multiple parallel executions
- **Monitoring**: Built-in execution logs

### **🔄 Workflow Comparison:**

| **Python App** | **n8n Template** |
|----------------|------------------|
| GUI trigger | Schedule trigger |
| Local file storage | Google Drive upload |
| Debug modes | Direct API calls |
| Manual execution | Automated execution |
| Local processing | Cloud processing |

## 🎛️ **Customization Options**

### **Story Themes:**
Modify OpenAI prompts for different themes:
- Urban legends
- Supernatural encounters  
- Psychological horror
- Haunted locations

### **Scheduling:**
- **Every minute**: Testing
- **Every hour**: Regular content
- **Daily**: Scheduled posts
- **Custom**: Specific times

### **Image Styles:**
Modify PiAPI prompts:
- Dark forests
- Abandoned buildings
- Mysterious figures
- Atmospheric landscapes

## 🐛 **Troubleshooting**

### **Common Issues:**
1. **API Rate Limits**: Increase wait times
2. **Credit Exhaustion**: Monitor API usage
3. **File Upload Errors**: Check Google Drive permissions
4. **Timeout Errors**: Increase wait node durations

### **Debug Tips:**
1. **Test Individual Nodes**: Run single steps
2. **Check API Responses**: Verify JSON structure
3. **Monitor Logs**: Use n8n execution logs
4. **Validate Keys**: Test API credentials

## 💰 **Cost Optimization**

### **Current Setup (10s Hedra):**
- **OpenAI**: ~$0.01 per story
- **ApyHub**: ~$0.02 per audio
- **PiAPI**: ~$0.05 per image
- **Hedra**: ~$1.50 per 10s video (vs $5 for 60s)
- **Total**: ~$1.58 per video (80% savings on Hedra!)

### **Monthly Estimates:**
- **Daily videos**: ~$47/month
- **Weekly videos**: ~$11/month
- **Bi-weekly**: ~$6/month

## 🎯 **Next Steps**

1. **Import template** into n8n
2. **Configure all API credentials**
3. **Test with manual execution**
4. **Set up scheduling**
5. **Monitor first automated runs**
6. **Optimize timing and prompts**

**Your TikTok automation is now ready for cloud deployment!** 🚀
