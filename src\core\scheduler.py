"""
Auto-Generation Scheduler for TikTok Automation
Optional automated video generation scheduling
"""

import os
import logging
import time
import asyncio
import threading
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime, timedelta
import json

class AutoGenerationScheduler:
    """Automated video generation scheduler"""
    
    def __init__(self, config: Dict[str, Any], db_manager, pipeline, account_manager):
        """Initialize auto-generation scheduler
        
        Args:
            config: Configuration dictionary
            db_manager: Database manager instance
            pipeline: Video generation pipeline
            account_manager: Enhanced account manager
        """
        self.config = config
        self.db_manager = db_manager
        self.pipeline = pipeline
        self.account_manager = account_manager
        
        # Scheduler state
        self.is_running = False
        self.scheduler_thread = None
        self.stop_event = threading.Event()
        
        # Scheduling configuration
        self.check_interval = 3600  # Check every hour
        self.max_concurrent_generations = 2
        self.generation_windows = {
            'morning': (8, 12),    # 8 AM - 12 PM
            'afternoon': (12, 17), # 12 PM - 5 PM
            'evening': (17, 22)    # 5 PM - 10 PM
        }
        
        # Statistics
        self.scheduled_generations = 0
        self.successful_generations = 0
        self.failed_generations = 0
        self.last_run_time = None
        
        # Callbacks
        self.status_callback = None
        self.generation_callback = None
    
    def set_callbacks(self, status_callback: Optional[Callable] = None,
                     generation_callback: Optional[Callable] = None):
        """Set callback functions
        
        Args:
            status_callback: Callback for status updates
            generation_callback: Callback for generation events
        """
        self.status_callback = status_callback
        self.generation_callback = generation_callback
    
    def start_scheduler(self):
        """Start the auto-generation scheduler"""
        if self.is_running:
            logging.warning("Scheduler is already running")
            return
        
        self.is_running = True
        self.stop_event.clear()
        
        # Start scheduler thread
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()
        
        logging.info("Auto-generation scheduler started")
        if self.status_callback:
            self.status_callback("Auto-generation scheduler started")
    
    def stop_scheduler(self):
        """Stop the auto-generation scheduler"""
        if not self.is_running:
            logging.warning("Scheduler is not running")
            return
        
        self.is_running = False
        self.stop_event.set()
        
        # Wait for thread to finish
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5.0)
        
        logging.info("Auto-generation scheduler stopped")
        if self.status_callback:
            self.status_callback("Auto-generation scheduler stopped")
    
    def _scheduler_loop(self):
        """Main scheduler loop"""
        while self.is_running and not self.stop_event.is_set():
            try:
                self.last_run_time = datetime.now()
                
                # Check if we should generate videos
                if self._should_generate_now():
                    asyncio.run(self._run_scheduled_generation())
                
                # Wait for next check
                self.stop_event.wait(self.check_interval)
                
            except Exception as e:
                logging.error(f"Error in scheduler loop: {str(e)}", exc_info=True)
                if self.status_callback:
                    self.status_callback(f"Scheduler error: {str(e)}")
                
                # Wait before retrying
                self.stop_event.wait(300)  # 5 minutes
    
    def _should_generate_now(self) -> bool:
        """Check if we should generate videos now
        
        Returns:
            bool: True if should generate
        """
        current_time = datetime.now()
        current_hour = current_time.hour
        
        # Check if we're in a valid generation window
        in_window = False
        for window_name, (start_hour, end_hour) in self.generation_windows.items():
            if start_hour <= current_hour < end_hour:
                in_window = True
                break
        
        if not in_window:
            return False
        
        # Check if we have accounts ready for generation
        ready_accounts = self.account_manager.get_accounts_for_generation()
        if not ready_accounts:
            return False
        
        # Additional checks can be added here (e.g., system load, API quotas)
        
        return True
    
    async def _run_scheduled_generation(self):
        """Run scheduled video generation"""
        try:
            logging.info("Starting scheduled video generation")
            if self.status_callback:
                self.status_callback("Starting scheduled video generation")
            
            # Get accounts ready for generation
            ready_accounts = self.account_manager.get_accounts_for_generation()
            
            if not ready_accounts:
                logging.info("No accounts ready for generation")
                return
            
            # Prioritize accounts by performance and need
            prioritized_accounts = self._prioritize_accounts(ready_accounts)
            
            # Limit to max concurrent generations
            accounts_to_process = prioritized_accounts[:self.max_concurrent_generations]
            
            logging.info(f"Generating videos for {len(accounts_to_process)} accounts")
            
            # Generate videos
            results = await self.pipeline.generate_batch(
                accounts=accounts_to_process,
                max_concurrent=self.max_concurrent_generations
            )
            
            # Process results
            for i, result in enumerate(results):
                account = accounts_to_process[i]
                self.scheduled_generations += 1
                
                if isinstance(result, dict) and result.get('success'):
                    self.successful_generations += 1
                    self.account_manager.record_generation_attempt(
                        account_id=account['id'],
                        success=True,
                        viral_score=result.get('viral_score', 0)
                    )
                    
                    if self.generation_callback:
                        self.generation_callback('success', account, result)
                    
                    logging.info(f"Scheduled generation successful for {account['username']}")
                    
                else:
                    self.failed_generations += 1
                    error_msg = str(result) if isinstance(result, Exception) else result.get('error', 'Unknown error')
                    
                    self.account_manager.record_generation_attempt(
                        account_id=account['id'],
                        success=False,
                        error=error_msg
                    )
                    
                    if self.generation_callback:
                        self.generation_callback('failure', account, {'error': error_msg})
                    
                    logging.error(f"Scheduled generation failed for {account['username']}: {error_msg}")
            
            success_count = sum(1 for r in results if isinstance(r, dict) and r.get('success'))
            
            if self.status_callback:
                self.status_callback(f"Scheduled generation completed: {success_count}/{len(results)} successful")
            
        except Exception as e:
            logging.error(f"Error in scheduled generation: {str(e)}", exc_info=True)
            if self.status_callback:
                self.status_callback(f"Scheduled generation error: {str(e)}")
    
    def _prioritize_accounts(self, accounts: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Prioritize accounts for generation
        
        Args:
            accounts: List of accounts to prioritize
            
        Returns:
            List: Prioritized accounts
        """
        def priority_score(account):
            performance = account.get('performance', {})
            
            # Base score
            score = 100
            
            # Prioritize accounts with higher success rates
            success_rate = performance.get('success_rate', 0)
            score += success_rate * 0.5
            
            # Prioritize accounts with higher viral scores
            viral_score = performance.get('avg_viral_score', 0)
            score += viral_score * 0.3
            
            # Prioritize accounts that haven't generated today
            daily_count = performance.get('daily_count', 0)
            max_daily = account.get('max_videos_per_day', 1)
            utilization = daily_count / max_daily
            score += (1 - utilization) * 50  # Higher score for less utilized accounts
            
            # Prioritize accounts that haven't generated recently
            last_generation = performance.get('last_generation')
            if last_generation:
                hours_since_last = (time.time() - last_generation) / 3600
                score += min(hours_since_last * 2, 20)  # Up to 20 points for time since last
            else:
                score += 20  # Never generated before
            
            return score
        
        # Sort by priority score (highest first)
        accounts.sort(key=priority_score, reverse=True)
        
        return accounts
    
    def get_scheduler_status(self) -> Dict[str, Any]:
        """Get scheduler status and statistics
        
        Returns:
            Dict: Scheduler status
        """
        return {
            'is_running': self.is_running,
            'last_run_time': self.last_run_time.isoformat() if self.last_run_time else None,
            'scheduled_generations': self.scheduled_generations,
            'successful_generations': self.successful_generations,
            'failed_generations': self.failed_generations,
            'success_rate': (self.successful_generations / self.scheduled_generations * 100) 
                           if self.scheduled_generations > 0 else 0,
            'check_interval': self.check_interval,
            'max_concurrent': self.max_concurrent_generations,
            'generation_windows': self.generation_windows
        }
    
    def update_settings(self, settings: Dict[str, Any]):
        """Update scheduler settings
        
        Args:
            settings: New settings to apply
        """
        if 'check_interval' in settings:
            self.check_interval = max(300, settings['check_interval'])  # Minimum 5 minutes
        
        if 'max_concurrent_generations' in settings:
            self.max_concurrent_generations = max(1, min(5, settings['max_concurrent_generations']))
        
        if 'generation_windows' in settings:
            self.generation_windows.update(settings['generation_windows'])
        
        logging.info("Scheduler settings updated")
        if self.status_callback:
            self.status_callback("Scheduler settings updated")
    
    def force_generation_check(self):
        """Force an immediate generation check"""
        if not self.is_running:
            logging.warning("Cannot force check: scheduler not running")
            return
        
        # Run generation check in a separate thread
        def run_check():
            try:
                asyncio.run(self._run_scheduled_generation())
            except Exception as e:
                logging.error(f"Error in forced generation check: {str(e)}")
        
        check_thread = threading.Thread(target=run_check, daemon=True)
        check_thread.start()
        
        logging.info("Forced generation check initiated")
        if self.status_callback:
            self.status_callback("Forced generation check initiated")
    
    def get_next_generation_time(self) -> Optional[datetime]:
        """Get the estimated next generation time
        
        Returns:
            datetime: Next generation time or None
        """
        if not self.is_running:
            return None
        
        current_time = datetime.now()
        current_hour = current_time.hour
        
        # Find next generation window
        for window_name, (start_hour, end_hour) in self.generation_windows.items():
            if current_hour < start_hour:
                # Next window today
                next_time = current_time.replace(hour=start_hour, minute=0, second=0, microsecond=0)
                return next_time
            elif start_hour <= current_hour < end_hour:
                # Currently in window, next check is soon
                return current_time + timedelta(seconds=self.check_interval)
        
        # Next window is tomorrow
        tomorrow = current_time + timedelta(days=1)
        first_window_start = min(start for start, _ in self.generation_windows.values())
        next_time = tomorrow.replace(hour=first_window_start, minute=0, second=0, microsecond=0)
        
        return next_time
