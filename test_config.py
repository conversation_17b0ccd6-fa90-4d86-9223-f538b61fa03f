#!/usr/bin/env python3
"""
Test TikTok Automation Configuration
"""

import json
import os
import sys

def test_config():
    """Test the current configuration"""
    
    print("🧪 Testing TikTok Automation Configuration...")
    print("=" * 50)
    
    # Load config
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
        print("✅ Config file loaded successfully")
    except Exception as e:
        print(f"❌ Error loading config: {e}")
        return False
    
    # Check providers
    tts_provider = config.get('tts', {}).get('provider', 'unknown')
    image_provider = config.get('image', {}).get('provider', 'unknown')
    
    print(f"\n📋 Configuration:")
    print(f"   TTS Provider: {tts_provider}")
    print(f"   Image Provider: {image_provider}")
    
    # Check if legacy workflow is properly configured
    if tts_provider == 'gtts' and image_provider == 'stable_diffusion':
        print("\n✅ LEGACY WORKFLOW CONFIGURED")
        
        # Test imports
        print("\n🔍 Testing imports...")
        try:
            from src.story_generation.story_generator import StoryGenerator
            print("   ✅ StoryGenerator")
        except Exception as e:
            print(f"   ❌ StoryGenerator: {e}")
            
        try:
            from src.tts.speech_generator import SpeechGenerator
            print("   ✅ SpeechGenerator")
        except Exception as e:
            print(f"   ❌ SpeechGenerator: {e}")
            
        try:
            from src.image_generation.image_generator import ImageGenerator
            print("   ✅ ImageGenerator")
        except Exception as e:
            print(f"   ❌ ImageGenerator: {e}")
            
        try:
            from src.video_generation.video_generator import VideoGenerator
            print("   ✅ VideoGenerator")
        except Exception as e:
            print(f"   ❌ VideoGenerator: {e}")
        
        # Test basic initialization
        print("\n🔧 Testing initialization...")
        try:
            story_gen = StoryGenerator(config)
            print("   ✅ StoryGenerator initialized")
        except Exception as e:
            print(f"   ❌ StoryGenerator init: {e}")
            
        try:
            speech_gen = SpeechGenerator(config)
            print("   ✅ SpeechGenerator initialized")
        except Exception as e:
            print(f"   ❌ SpeechGenerator init: {e}")
            
        try:
            image_gen = ImageGenerator(config)
            print("   ✅ ImageGenerator initialized")
        except Exception as e:
            print(f"   ❌ ImageGenerator init: {e}")
            
        try:
            video_gen = VideoGenerator(config)
            print("   ✅ VideoGenerator initialized")
        except Exception as e:
            print(f"   ❌ VideoGenerator init: {e}")
        
        print("\n🎉 CONFIGURATION TEST PASSED!")
        print("\n🚀 Ready to run: python main.py")
        return True
        
    else:
        print(f"\n❌ CONFIGURATION MISMATCH")
        print(f"   Expected: gtts + stable_diffusion")
        print(f"   Found: {tts_provider} + {image_provider}")
        return False

def check_directories():
    """Check and create required directories"""
    print("\n📁 Checking directories...")
    
    dirs = ['data', 'data/audio', 'data/images', 'data/videos', 'output']
    
    for dir_path in dirs:
        if os.path.exists(dir_path):
            print(f"   ✅ {dir_path}")
        else:
            try:
                os.makedirs(dir_path, exist_ok=True)
                print(f"   📁 Created {dir_path}")
            except Exception as e:
                print(f"   ❌ Failed to create {dir_path}: {e}")

if __name__ == "__main__":
    print("🎬 TikTok Automation Configuration Test")
    print("=" * 50)
    
    # Add current directory to Python path
    sys.path.insert(0, os.getcwd())
    
    # Run tests
    config_ok = test_config()
    check_directories()
    
    print("\n" + "=" * 50)
    if config_ok:
        print("🎉 ALL TESTS PASSED!")
        print("\n📝 Test Data:")
        print("   Account: test_horror_account")
        print("   Niche: horror")
        print("   Theme: Scary Stories")
        print("   Topic: A person finds an old diary that writes itself")
        print("   Voice: female")
        print("\n🚀 Run the app: python main.py")
    else:
        print("❌ TESTS FAILED")
        print("🔧 Please fix the configuration issues above")
    
    input("\nPress Enter to continue...")
