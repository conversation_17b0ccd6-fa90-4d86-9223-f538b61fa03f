"""
Image Hosting Service for TikTok Automation
Temporary image hosting for API integrations
"""

import os
import logging
import time
import asyncio
from typing import Dict, Any, Optional
import base64

try:
    import httpx
except ImportError:
    logging.warning("httpx package not installed. Image hosting will not be available.")

class ImageHostingService:
    """Service for temporary image hosting"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize image hosting service
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.provider = config['ai_workflow']['image_hosting']['provider']
        self.backup_provider = config['ai_workflow']['image_hosting']['backup_provider']
        self.imgbb_api_key = config['api_keys']['imgbb']
        
        # API endpoints
        self.imgbb_url = "https://api.imgbb.com/1/upload"
        self.freeimage_url = "https://freeimage.host/api/1/upload"
        
        # Initialize HTTP client
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def upload_image(self, image_path: str, expiration: int = 3600) -> Dict[str, Any]:
        """Upload image to temporary hosting service
        
        Args:
            image_path: Path to the image file
            expiration: Expiration time in seconds (default: 1 hour)
            
        Returns:
            Dict containing upload result with URL
        """
        logging.info(f"Uploading image to {self.provider}: {image_path}")
        
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"Image file not found: {image_path}")
        
        try:
            # Try primary provider first
            if self.provider == "imgbb":
                result = await self._upload_to_imgbb(image_path, expiration)
            else:
                result = await self._upload_to_freeimage(image_path)
            
            logging.info(f"Image uploaded successfully: {result['url']}")
            return result
            
        except Exception as e:
            logging.warning(f"Primary provider failed: {str(e)}, trying backup")
            
            # Try backup provider
            try:
                if self.backup_provider == "freeimage.host":
                    result = await self._upload_to_freeimage(image_path)
                else:
                    result = await self._upload_to_imgbb(image_path, expiration)
                
                logging.info(f"Image uploaded to backup provider: {result['url']}")
                return result
                
            except Exception as backup_error:
                logging.error(f"Both providers failed. Last error: {str(backup_error)}")
                raise Exception(f"Image upload failed on both providers: {str(e)}, {str(backup_error)}")
    
    async def _upload_to_imgbb(self, image_path: str, expiration: int) -> Dict[str, Any]:
        """Upload image to ImgBB
        
        Args:
            image_path: Path to the image file
            expiration: Expiration time in seconds
            
        Returns:
            Dict containing upload result
        """
        if not self.imgbb_api_key:
            raise ValueError("ImgBB API key not configured")
        
        # Read and encode image
        with open(image_path, 'rb') as image_file:
            image_data = base64.b64encode(image_file.read()).decode('utf-8')
        
        # Prepare request data
        data = {
            'key': self.imgbb_api_key,
            'image': image_data,
            'expiration': expiration
        }
        
        # Upload image
        response = await self.client.post(self.imgbb_url, data=data)
        response.raise_for_status()
        
        result = response.json()
        
        if not result.get('success'):
            raise Exception(f"ImgBB upload failed: {result.get('error', 'Unknown error')}")
        
        return {
            'provider': 'imgbb',
            'url': result['data']['url'],
            'delete_url': result['data']['delete_url'],
            'expiration': expiration,
            'uploaded_at': time.time()
        }
    
    async def _upload_to_freeimage(self, image_path: str) -> Dict[str, Any]:
        """Upload image to FreeImage.host
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Dict containing upload result
        """
        # Read image file
        with open(image_path, 'rb') as image_file:
            files = {'source': image_file}
            data = {'type': 'file', 'action': 'upload'}
            
            # Upload image
            response = await self.client.post(self.freeimage_url, files=files, data=data)
            response.raise_for_status()
        
        result = response.json()
        
        if not result.get('success'):
            raise Exception(f"FreeImage upload failed: {result.get('error', 'Unknown error')}")
        
        return {
            'provider': 'freeimage.host',
            'url': result['image']['url'],
            'delete_url': result['image']['delete_url'],
            'expiration': None,  # FreeImage doesn't have expiration
            'uploaded_at': time.time()
        }
    
    async def delete_image(self, delete_url: str) -> bool:
        """Delete uploaded image
        
        Args:
            delete_url: Delete URL from upload response
            
        Returns:
            bool: True if successful
        """
        try:
            response = await self.client.get(delete_url)
            response.raise_for_status()
            logging.info(f"Image deleted successfully: {delete_url}")
            return True
        except Exception as e:
            logging.warning(f"Failed to delete image: {str(e)}")
            return False
    
    async def cleanup_expired_images(self, upload_records: list) -> int:
        """Clean up expired images
        
        Args:
            upload_records: List of upload records with delete URLs
            
        Returns:
            int: Number of images cleaned up
        """
        cleaned_count = 0
        current_time = time.time()
        
        for record in upload_records:
            # Check if image has expired
            if record.get('expiration'):
                upload_time = record.get('uploaded_at', 0)
                if current_time - upload_time > record['expiration']:
                    if await self.delete_image(record['delete_url']):
                        cleaned_count += 1
        
        logging.info(f"Cleaned up {cleaned_count} expired images")
        return cleaned_count
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()
    
    def __del__(self):
        """Cleanup on destruction"""
        try:
            asyncio.create_task(self.close())
        except:
            pass
