"""
Debug Configuration Module
Handles debug mode settings to avoid consuming API credits during testing
"""

import os
import logging
from pathlib import Path
from dotenv import load_dotenv

class DebugConfig:
    """Debug configuration manager"""

    def __init__(self):
        # Load environment variables from .env file
        load_dotenv()

        # Debug mode settings
        self.debug_mode = os.getenv('DEBUG', 'false').lower() == 'true'

        # Individual API debug flags (only used when debug_mode is True)
        self.apyhub_audio_debug = os.getenv('APYHUB_AUDIO_DEBUG', 'false').lower() == 'true'
        self.piapi_image_debug = os.getenv('PIAPI_IMAGE_DEBUG', 'false').lower() == 'true'
        self.hedra_video_debug = os.getenv('HEDRA_VIDEO_DEBUG', 'false').lower() == 'true'

        # Pre-generated file paths
        self.debug_audio_file = os.getenv('DEBUG_AUDIO_FILE', 'data/audio/speech_1748637306.mp3')
        self.debug_base_image_file = os.getenv('DEBUG_BASE_IMAGE_FILE', 'data/images/image_1748637312.png')
        self.debug_enhanced_image_file = os.getenv('DEBUG_ENHANCED_IMAGE_FILE', 'output/MyTikTokTest/enhanced_image_gen_1748637292.jpg')

        # Log debug configuration
        if self.debug_mode:
            logging.info("🔧 DEBUG MODE ENABLED - Using pre-generated files to save API credits")
            logging.info(f"  - ApyHub Audio Debug: {self.apyhub_audio_debug}")
            logging.info(f"  - PiAPI Image Debug: {self.piapi_image_debug}")
            logging.info(f"  - Hedra Video Debug: {self.hedra_video_debug}")
        else:
            logging.info("🚀 PRODUCTION MODE - Using live APIs")

    def should_use_debug_audio(self) -> bool:
        """Check if should use debug audio file instead of ApyHub API"""
        return self.debug_mode and self.apyhub_audio_debug

    def should_use_debug_image(self) -> bool:
        """Check if should use debug image file instead of PiAPI"""
        return self.debug_mode and self.piapi_image_debug

    def should_use_debug_video(self) -> bool:
        """Check if should use debug video generation instead of Hedra API"""
        return self.debug_mode and self.hedra_video_debug

    def get_debug_audio_path(self) -> str:
        """Get path to debug audio file"""
        path = self.debug_audio_file
        # Convert to absolute path if it's relative
        if not os.path.isabs(path):
            path = os.path.abspath(path)
        return path

    def get_debug_base_image_path(self) -> str:
        """Get path to debug base image file"""
        path = self.debug_base_image_file
        # Convert to absolute path if it's relative
        if not os.path.isabs(path):
            path = os.path.abspath(path)
        return path

    def get_debug_enhanced_image_path(self) -> str:
        """Get path to debug enhanced image file"""
        path = self.debug_enhanced_image_file
        # Convert to absolute path if it's relative
        if not os.path.isabs(path):
            path = os.path.abspath(path)
        return path

    def verify_debug_files(self) -> bool:
        """Verify that all debug files exist"""
        if not self.debug_mode:
            return True

        files_to_check = []

        if self.apyhub_audio_debug:
            files_to_check.append(self.debug_audio_file)

        if self.piapi_image_debug:
            files_to_check.append(self.debug_base_image_file)
            files_to_check.append(self.debug_enhanced_image_file)

        missing_files = []
        for file_path in files_to_check:
            if not Path(file_path).exists():
                missing_files.append(file_path)

        if missing_files:
            logging.warning(f"⚠️ Missing debug files: {missing_files}")
            return False

        logging.info("All debug files verified")
        return True

# Global debug configuration instance
debug_config = DebugConfig()
