#!/usr/bin/env python3
"""
Test script to verify ApyHub TTS API integration
"""

import os
import tempfile
import requests
import asyncio
import httpx

# Load API key from config
import json
with open('config.json', 'r') as f:
    config = json.load(f)

API_TOKEN = config['api_keys']['apyhub']
API_URL = 'https://api.apyhub.com/tts/file'

def test_sync_requests():
    """Test with synchronous requests library"""
    print("🔍 Testing ApyHub TTS with requests library...")
    
    # Create a simple text file
    test_text = "Hello, this is a test message for ApyHub text to speech API."
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as temp_file:
        temp_file.write(test_text)
        temp_file_path = temp_file.name
    
    try:
        # Prepare the file and parameters
        with open(temp_file_path, 'rb') as f:
            files = {
                'file': ('test.txt', f, 'text/plain')
            }
            data = {
                'gender': 'female'
            }
            headers = {
                'apy-token': API_TOKEN
            }

            # Send the POST request
            print(f"Sending request to: {API_URL}")
            print(f"Headers: {headers}")
            print(f"Data: {data}")
            print(f"File size: {os.path.getsize(temp_file_path)} bytes")
            
            response = requests.post(API_URL, headers=headers, files=files, data=data)

        # Handle the response
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            output_path = 'test_output_sync.mp3'
            with open(output_path, 'wb') as f:
                f.write(response.content)
            print(f"✅ Audio file saved as '{output_path}'")
            return True
        else:
            print(f"❌ Error: {response.status_code} - {response.text}")
            return False
            
    finally:
        # Clean up temp file
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)

async def test_async_httpx():
    """Test with async httpx library"""
    print("\n🔍 Testing ApyHub TTS with httpx library...")
    
    # Create a simple text file
    test_text = "Hello, this is a test message for ApyHub text to speech API using httpx."
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as temp_file:
        temp_file.write(test_text)
        temp_file_path = temp_file.name
    
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            # Prepare the file and parameters
            with open(temp_file_path, 'rb') as f:
                files = {
                    'file': ('test.txt', f, 'text/plain')
                }
                data = {
                    'gender': 'female'
                }
                headers = {
                    'apy-token': API_TOKEN
                }

                # Send the POST request
                print(f"Sending async request to: {API_URL}")
                print(f"Headers: {headers}")
                print(f"Data: {data}")
                print(f"File size: {os.path.getsize(temp_file_path)} bytes")
                
                response = await client.post(API_URL, headers=headers, files=files, data=data)

            # Handle the response
            print(f"Response status: {response.status_code}")
            print(f"Response headers: {dict(response.headers)}")
            
            if response.status_code == 200:
                output_path = 'test_output_async.mp3'
                with open(output_path, 'wb') as f:
                    f.write(response.content)
                print(f"✅ Audio file saved as '{output_path}'")
                return True
            else:
                print(f"❌ Error: {response.status_code} - {response.text}")
                return False
                
    finally:
        # Clean up temp file
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)

def test_api_key():
    """Test if API key is valid"""
    print(f"\n🔍 Testing API key...")
    print(f"API Key: {API_TOKEN[:10]}...{API_TOKEN[-4:] if len(API_TOKEN) > 14 else 'TOO_SHORT'}")
    print(f"API Key length: {len(API_TOKEN)}")
    
    if not API_TOKEN or len(API_TOKEN) < 10:
        print("❌ API key appears to be invalid or missing")
        return False
    else:
        print("✅ API key format looks valid")
        return True

async def main():
    """Main test function"""
    print("🚀 ApyHub TTS API Test Suite")
    print("=" * 50)
    
    # Test 1: API key validation
    if not test_api_key():
        print("❌ API key test failed. Please check your configuration.")
        return
    
    # Test 2: Sync requests
    sync_success = test_sync_requests()
    
    # Test 3: Async httpx
    async_success = await test_async_httpx()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"Sync requests: {'✅ PASS' if sync_success else '❌ FAIL'}")
    print(f"Async httpx: {'✅ PASS' if async_success else '❌ FAIL'}")
    
    if sync_success or async_success:
        print("\n🎉 At least one method worked! ApyHub API is accessible.")
    else:
        print("\n❌ Both methods failed. Please check API key and network connection.")

if __name__ == "__main__":
    asyncio.run(main())
