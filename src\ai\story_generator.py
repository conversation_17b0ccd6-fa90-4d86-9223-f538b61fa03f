"""
Enhanced Story Generator for TikTok Automation
Advanced OpenAI integration with optimized prompts for viral content
"""

import os
import logging
import time
import asyncio
from typing import Dict, Any, Optional, List, Tuple
import json

try:
    import openai
except ImportError:
    logging.warning("OpenAI package not installed. Enhanced story generation will not be available.")

class EnhancedStoryGenerator:
    """Enhanced story generator with viral-optimized prompts"""

    def __init__(self, config: Dict[str, Any]):
        """Initialize enhanced story generator

        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.api_key = config['api_keys']['openai']
        self.model = config['ai']['story_generation']['model']
        self.temperature = config['ai']['story_generation']['temperature']
        self.max_tokens = config['ai']['story_generation']['max_tokens']

        # Initialize OpenAI client
        if self.api_key:
            openai.api_key = self.api_key
        else:
            logging.warning("OpenAI API key not configured")

    async def generate_viral_story(self, niche: str, duration_seconds: int,
                                   additional_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Generate a viral-optimized story for TikTok

        Args:
            niche: Content niche (e.g., "Haunted House", "Mystery", "Horror")
            duration_seconds: Target video duration in seconds
            additional_context: Additional context for story generation

        Returns:
            Dict containing the generated story and metadata
        """
        logging.info(f"Generating viral story for niche: {niche}, duration: {duration_seconds}s")

        # Calculate target word count (150 words per minute average speaking rate)
        target_word_count = int((duration_seconds / 60) * 150)

        # Create viral-optimized prompt
        prompt = self._create_viral_prompt(niche, target_word_count, additional_context)

        try:
            # Generate story using OpenAI
            response = await self._generate_with_openai_async(prompt)
            story_text = response.strip()

            # Extract title and clean up text
            title, clean_story = self._extract_title_and_story(story_text)

            # Calculate metrics
            word_count = len(clean_story.split())
            estimated_duration = int((word_count / 150) * 60)

            result = {
                'title': title,
                'text': clean_story,
                'niche': niche,
                'word_count': word_count,
                'estimated_duration': estimated_duration,
                'target_duration': duration_seconds,
                'viral_score': self._calculate_viral_score(clean_story, niche),
                'timestamp': time.time()
            }

            logging.info(f"Generated viral story: {title} ({word_count} words, ~{estimated_duration}s)")
            return result

        except Exception as e:
            logging.error(f"Error generating viral story: {str(e)}")
            raise

    def _create_viral_prompt(self, niche: str, target_word_count: int,
                            additional_context: Optional[Dict[str, Any]] = None) -> str:
        """Create a viral-optimized prompt for story generation

        Args:
            niche: Content niche
            target_word_count: Target word count
            additional_context: Additional context

        Returns:
            str: Optimized prompt for viral content
        """
        # Viral content strategies by niche
        viral_strategies = {
            "horror": "Start with immediate tension, use cliffhangers, build suspense gradually",
            "mystery": "Begin with an intriguing question, reveal clues progressively, end with revelation",
            "haunted house": "Create atmospheric dread, use sensory details, escalate supernatural events",
            "true crime": "Start with shocking fact, build timeline, focus on human psychology",
            "paranormal": "Establish normalcy first, introduce unexplained phenomena, build to climax"
        }

        strategy = viral_strategies.get(niche.lower(), "Create engaging hook, build tension, deliver satisfying conclusion")

        prompt = f"""Create a viral TikTok story for the "{niche}" niche that will captivate viewers and encourage engagement.

VIRAL CONTENT REQUIREMENTS:
- Hook viewers in the first 3 seconds with a compelling opening
- Target length: {target_word_count} words (for {int(target_word_count/150*60)}-second video)
- Use conversational, easy-to-follow language perfect for narration
- Include emotional triggers and relatable elements
- Build suspense and maintain engagement throughout
- End with impact or call-to-action
- VISUAL FOCUS: Include vivid, cinematic descriptions perfect for anime-style artwork

STRATEGY FOR THIS NICHE: {strategy}

VISUAL STORYTELLING REQUIREMENTS:
- Describe atmospheric settings (dark forests, moonlit rivers, mysterious castles, ancient temples)
- Include dramatic lighting elements (shadows, moonbeams, flickering lights, glowing objects)
- Mention specific visual details that would look amazing in anime style
- Focus on cinematic, dramatic scenes that create visual impact
- Use environmental storytelling (weather, time of day, mysterious locations)

STRUCTURE:
1. HOOK (first 10-15 words): Start with something shocking, mysterious, or intriguing
2. SETUP: Establish context quickly with vivid visual descriptions
3. BUILD: Develop tension/interest with atmospheric and visual details
4. CLIMAX: Deliver the main revelation in a visually dramatic setting
5. RESOLUTION: Provide satisfying conclusion that encourages comments/shares

TONE: Conversational, engaging, slightly dramatic but believable
STYLE: Short sentences, active voice, vivid imagery, cinematic descriptions
AVOID: Complex explanations, too many characters, confusing timelines

VISUAL EXAMPLES FOR INSPIRATION:
- "Deep in the moonlit forest where ancient trees cast twisted shadows..."
- "The old mansion stood silhouetted against storm clouds..."
- "Mist rolled across the dark river as something stirred beneath..."
- "In the candlelit basement where shadows danced on stone walls..."

Generate the story now with rich visual descriptions:"""

        return prompt

    async def _generate_with_openai_async(self, prompt: str) -> str:
        """Generate story using OpenAI API asynchronously

        Args:
            prompt: Prompt for story generation

        Returns:
            str: Generated story text
        """
        try:
            # Use asyncio to run the synchronous OpenAI call
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: openai.chat.completions.create(
                    model=self.model,
                    messages=[
                        {"role": "system", "content": "You are a viral content creator specializing in TikTok storytelling. Create engaging, shareable stories that hook viewers immediately."},
                        {"role": "user", "content": prompt}
                    ],
                    temperature=self.temperature,
                    max_tokens=self.max_tokens
                )
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logging.error(f"Error with OpenAI API: {str(e)}")
            raise

    def _extract_title_and_story(self, story_text: str) -> Tuple[str, str]:
        """Extract title and clean story text

        Args:
            story_text: Raw story text from AI

        Returns:
            tuple: (title, clean_story_text)
        """
        lines = story_text.strip().split('\n')

        # Look for title patterns
        title = "Untitled Story"
        story_start_idx = 0

        for i, line in enumerate(lines):
            line = line.strip()
            if line and (line.startswith('Title:') or line.startswith('**') or
                        line.isupper() and len(line.split()) <= 6):
                title = line.replace('Title:', '').replace('**', '').strip()
                story_start_idx = i + 1
                break

        # Extract clean story text
        story_lines = []
        for line in lines[story_start_idx:]:
            line = line.strip()
            if line and not line.startswith('Title:') and not line.startswith('**Title'):
                story_lines.append(line)

        clean_story = ' '.join(story_lines)

        # Fallback: use first few words as title if no title found
        if title == "Untitled Story" and clean_story:
            words = clean_story.split()
            title = ' '.join(words[:5]) + "..."

        return title, clean_story

    def _calculate_viral_score(self, story_text: str, niche: str) -> float:
        """Calculate a viral potential score for the story

        Args:
            story_text: Story text to analyze
            niche: Content niche

        Returns:
            float: Viral score (0-100)
        """
        score = 50.0  # Base score

        # Check for viral elements
        viral_words = ['shocking', 'unbelievable', 'mysterious', 'terrifying', 'amazing',
                      'incredible', 'secret', 'hidden', 'revealed', 'discovered']

        engagement_words = ['you', 'your', 'imagine', 'picture', 'what if', 'suddenly',
                           'then', 'but', 'however', 'until']

        # Count viral and engagement words
        text_lower = story_text.lower()
        viral_count = sum(1 for word in viral_words if word in text_lower)
        engagement_count = sum(1 for word in engagement_words if word in text_lower)

        # Adjust score based on elements
        score += viral_count * 5
        score += engagement_count * 3

        # Check story structure
        sentences = story_text.split('.')
        if len(sentences) >= 3:  # Good structure
            score += 10

        # Check for strong opening
        first_words = story_text.split()[:10]
        if any(word.lower() in ['imagine', 'what', 'you', 'have', 'never'] for word in first_words):
            score += 15

        return min(100.0, max(0.0, score))

    def refine_prompt_with_image_description(self, base_prompt: str, image_description: str,
                                           niche: str) -> str:
        """Refine image generation prompt using Midjourney description and niche

        Args:
            base_prompt: Base prompt for image generation
            image_description: Description from Midjourney describe
            niche: Content niche

        Returns:
            str: Refined prompt for better image generation
        """
        refinement_prompt = f"""Create an enhanced anime-style image generation prompt for TikTok content.

BASE PROMPT: {base_prompt}
NICHE: {niche}
IMAGE DESCRIPTION: {image_description}

Create an enhanced prompt that:
1. Transforms the concept into anime/manga art style
2. Incorporates dramatic, cinematic visual elements
3. Optimizes for the {niche} niche with atmospheric details
4. Includes specific anime-style lighting and effects
5. Maintains TikTok vertical format (9:16 aspect ratio)
6. Focuses on environmental storytelling and mood

ANIME STYLE REQUIREMENTS:
- Dramatic lighting (moonbeams, shadows, glowing effects)
- Atmospheric elements (mist, fog, particles, sparkles)
- Cinematic composition with depth
- Rich color palette with contrast
- Environmental details (ancient trees, flowing water, mysterious architecture)
- Emotional atmosphere matching the story mood

EXAMPLE ENHANCED ELEMENTS:
- "moonlit forest with ancient twisted trees casting long shadows"
- "mysterious mist rolling through a dark valley"
- "dramatic storm clouds with lightning illuminating a castle"
- "glowing particles floating in a mystical atmosphere"

Enhanced anime-style prompt:"""

        try:
            response = openai.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are an expert at creating optimized prompts for AI image generation, specializing in viral TikTok content."},
                    {"role": "user", "content": refinement_prompt}
                ],
                temperature=0.7,
                max_tokens=200
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logging.error(f"Error refining prompt: {str(e)}")
            return base_prompt  # Fallback to original prompt

    def transform_scene_to_visual_prompt(self, scene_description: str) -> str:
        """Transform scene description into visual prompt for image generation

        Args:
            scene_description: Scene description from Midjourney describe

        Returns:
            str: Visual prompt for baby transformation
        """
        system_prompt = """You are an expert prompt engineer specialising in crafting hyperrealistic visual generation prompts from reference Midjourney description. Your job is to analyse the given image and describe here it with precise, vivid, and structured language optimised for AI image generation models (e.g., Midjourney). Prioritise clarity, object specificity, photorealism, physical proportions, and environmental context. Remove any branding, UI elements, or text from the description. Avoid generic phrases. Do not include actual names unless specifically instructed.

Also, do not include "\\n" or any other code within the prompt generated!"""

        user_prompt = f"""=You are an advanced visual transformation AI specialised in generating photorealistic baby versions of described subjects while preserving all key visual, spatial, and contextual elements from the provided scene description.

Scene Description: {scene_description}

Your task is to transform the primary subject described in the scene into a photorealistic baby version of themselves, while maintaining their pose, expression, clothing, environment, and overall context exactly as described. This must not be a cartoon or stylized rendering — it is a 1:1 naturalistic reinterpretation based strictly on the textual description.

Transformation Objectives:

1. Facial Continuity & Realism
• Retain the subject's core identity — including facial structure, skin tone, and distinguishing features — adjusted only to reflect natural baby proportions.
• Eyes should remain recognisably expressive and proportionally enlarged to match infant symmetry.
• Skin must appear smooth, soft, and realistic, with no artificial gloss or exaggeration.

2. Pose & Expression Fidelity
• Maintain the exact pose, body orientation, and facial expression as described.
• Adjust limbs to match the proportions of a seated 16-month-old baby while preserving gesture and interaction with the environment.

3. Clothing & Accessories Consistency
• Preserve all described clothing, accessories, and visible textures — resizing elements as needed for a baby's body without distortion.
• Do not introduce baby-specific items or modify the wardrobe in any way.

4. Environmental Preservation
• Reproduce the environment in full detail as described — including background objects, signage, lighting, and layout.
• Keep camera angle, depth of field, and light direction intact and the exact same as described.

5. Photorealistic Rendering Goals
• Ensure all elements — lighting, shadows, reflections, and materials — appear physically plausible and naturally rendered.
• Avoid any synthetic effects, cartoon exaggeration, or artificial smoothness.

6. Strict Visual Constraints
• Do not add or remove any objects.
• Do not alter scene composition or perspective.
• No overlays, text, UI elements, watermarks, or framing effects.

The result should be a single high-fidelity prompt to generate an image, fully aligned with the description, and in raw text output only. Do not prepend "Output:" or include line breaks like "/n".

DON'T INCLUDE LINE BREAKS ANYWHERE IN THE RESPONSE, no \\n.

Ensure the full prompt remains under 700 characters."""

        try:
            response = openai.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.7,
                max_tokens=300
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logging.error(f"Error transforming scene to visual prompt: {str(e)}")
            return scene_description  # Fallback to original description
