# 🎬 TikTok Automation n8n Template Guide

## 📋 **Overview**

This n8n workflow template automates the complete TikTok video creation process using your simplified pipeline with only 5 AI services.

### **🎯 Workflow Steps:**
1. **OpenAI (GPT-4)** → Generate viral TikTok stories
2. **ApyHub** → Convert script to audio (Azure Neural Voices)
3. **OpenAI (GPT-4)** → Create visual scene prompts
4. **PiAPI (Midjourney)** → Generate anime-style images
5. **Hedra** → Combine audio + image into video
6. **Google Drive** → Upload final TikTok-ready video

## 🚀 **Quick Setup**

### **1. Import Template**
1. Open n8n
2. Click "Templates" → "Import from file"
3. Upload `tiktok-automation-n8n-template.json`
4. The workflow will be imported with all nodes

### **2. Configure API Credentials**
You need to set up credentials for each service:

#### **OpenAI Credentials**
- **Type**: OpenAI
- **API Key**: Your OpenAI API key
- **Name**: `openai-credentials`

#### **ApyHub Credentials**
- **Type**: Generic Credential
- **Name**: `apyhub`
- **Fields**:
  - `token`: Your ApyHub API key

#### **PiAPI Credentials**
- **Type**: Generic Credential  
- **Name**: `piapi`
- **Fields**:
  - `apiKey`: Your PiAPI API key

#### **Hedra Credentials**
- **Type**: Generic Credential
- **Name**: `hedra`
- **Fields**:
  - `apiKey`: Your Hedra API key

#### **Google Drive Credentials**
- **Type**: Google Drive OAuth2
- **Name**: `google-drive-credentials`
- **Setup**: Follow n8n's Google Drive OAuth setup

### **3. Update Google Drive Folder**
1. Find the "Upload to Google Drive" node
2. Replace `REPLACE_WITH_YOUR_FOLDER_ID` with your actual Google Drive folder ID
3. To get folder ID: Open folder in Google Drive, copy ID from URL

## 🔧 **Configuration Details**

### **Webhook Trigger**
- **URL**: `https://your-n8n-instance.com/webhook/tiktok-automation`
- **Method**: POST
- **Payload Example**:
```json
{
  "topic": "mysterious adventure in a dark forest"
}
```

### **Story Generation (OpenAI)**
- **Model**: GPT-4
- **Purpose**: Generate viral TikTok scripts
- **Input**: Topic from webhook payload
- **Output**: 45-60 second script optimized for TikTok

### **Audio Generation (ApyHub)**
- **Voice**: en-US-AriaNeural (Azure Neural Voice)
- **Format**: MP3
- **Quality**: High-quality TTS
- **Processing Time**: ~30 seconds

### **Visual Prompt Creation (OpenAI)**
- **Model**: GPT-4
- **Purpose**: Transform story into visual prompts
- **Style**: Anime-optimized for Midjourney
- **Output**: Detailed visual description

### **Image Generation (PiAPI)**
- **Model**: Midjourney v6
- **Style**: Anime with --ar 9:16
- **Quality**: High-resolution
- **Processing Time**: ~5 minutes

### **Video Assembly (Hedra)**
- **Resolution**: 720p
- **Aspect Ratio**: 9:16 (TikTok format)
- **Duration**: ~60 seconds
- **Style**: Anime with natural movement
- **Processing Time**: ~8 minutes

## 📊 **Expected Costs Per Video**

| Service | Cost | Purpose |
|---------|------|---------|
| OpenAI (GPT-4) | ~$0.10 | Story + prompt generation |
| ApyHub | ~$0.01 | Text-to-audio conversion |
| PiAPI (Midjourney) | ~$0.08 | Image generation |
| Hedra | ~$0.30 | Video assembly |
| **Total** | **~$0.49** | **Per TikTok video** |

## ⏱️ **Processing Times**

| Step | Duration | Notes |
|------|----------|-------|
| Story Generation | 10-30 seconds | GPT-4 processing |
| Audio Generation | 30 seconds | ApyHub processing |
| Visual Prompt | 10-30 seconds | GPT-4 processing |
| Image Generation | 5 minutes | Midjourney processing |
| Video Assembly | 8 minutes | Hedra processing |
| **Total** | **~14 minutes** | **End-to-end** |

## 🎮 **How to Use**

### **Method 1: Webhook Trigger**
```bash
curl -X POST https://your-n8n-instance.com/webhook/tiktok-automation \
  -H "Content-Type: application/json" \
  -d '{"topic": "scary story about a haunted house"}'
```

### **Method 2: Manual Execution**
1. Open the workflow in n8n
2. Click "Execute Workflow"
3. Enter topic in the webhook data
4. Watch the automation run

### **Method 3: Scheduled Execution**
1. Replace webhook trigger with Cron trigger
2. Set schedule (e.g., daily at 9 AM)
3. Add topic randomization logic

## 📁 **Output Structure**

Videos are uploaded to Google Drive with this naming:
```
tiktok-video-{timestamp}.mp4
```

**Video Specifications:**
- **Format**: MP4
- **Resolution**: 720p (1280x720)
- **Aspect Ratio**: 9:16 (vertical)
- **Duration**: 45-60 seconds
- **Style**: Anime with natural movement
- **Audio**: High-quality Azure Neural Voice

## 🔍 **Monitoring & Debugging**

### **Check Execution Status**
1. Go to n8n "Executions" tab
2. View real-time progress
3. Check each node's output
4. Monitor API responses

### **Common Issues**
- **API Rate Limits**: Add longer wait times
- **Credential Errors**: Verify API keys
- **Timeout Issues**: Increase wait durations
- **Google Drive Permissions**: Check OAuth scope

### **Success Indicators**
- ✅ Story generated successfully
- ✅ Audio file downloaded
- ✅ Image generated and downloaded
- ✅ Video assembled by Hedra
- ✅ File uploaded to Google Drive

## 🎯 **Customization Options**

### **Change Voice**
In "ApyHub - Text to Audio" node, modify:
```json
{
  "voice": "en-US-JennyNeural"  // Female voice
  "voice": "en-US-DavisNeural"  // Male voice
}
```

### **Adjust Video Duration**
In "Hedra - Generate Video" node, modify:
```json
{
  "duration_ms": 45000  // 45 seconds
}
```

### **Change Image Style**
In "PiAPI - Generate Image" node, modify prompt:
```
"{{ prompt }} --ar 9:16 --style raw --v 6"  // Realistic style
"{{ prompt }} --ar 9:16 --style cute --v 6"  // Cute style
```

## 🚀 **Next Steps**

1. **Import the template** into your n8n instance
2. **Configure all API credentials** 
3. **Update Google Drive folder ID**
4. **Test with a simple topic**
5. **Monitor the complete execution**
6. **Download and review the generated video**
7. **Upload to TikTok and track performance**

## 📞 **Support**

If you encounter issues:
1. Check the n8n execution logs
2. Verify all API credentials
3. Ensure sufficient API credits
4. Review the node configurations
5. Test individual nodes separately

Your TikTok automation pipeline is now ready to create viral content automatically! 🎬✨
