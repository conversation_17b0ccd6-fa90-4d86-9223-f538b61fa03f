"""
Image Generator for TikTok Automation
Generates cover images for TikTok videos
"""

import os
import logging
import time
import io
import base64
import requests
from typing import Dict, Any, Optional, Tuple
from PIL import Image, ImageOps, ImageFilter, ImageEnhance

class ImageGenerator:
    """Generates cover images for TikTok videos"""

    def __init__(self, config: Dict[str, Any]):
        """Initialize image generator

        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.provider = config['image']['provider']
        self.width = config['image']['resolution']['width']
        self.height = config['image']['resolution']['height']

        # Create output directory
        self.output_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
                                      "data", "images")
        os.makedirs(self.output_dir, exist_ok=True)

        # Initialize provider-specific resources
        if self.provider == 'stable_diffusion':
            self._init_stable_diffusion()
        elif self.provider == 'craiyon':
            self._init_craiyon()
        elif self.provider == 'piapi_midjourney':
            self._init_piapi_midjourney()
        else:
            raise ValueError(f"Unsupported image generation provider: {self.provider}")

    def _init_stable_diffusion(self):
        """Initialize Stable Diffusion API"""
        self.api_key = self.config['api_keys'].get('stability', os.environ.get('STABLE_DIFFUSION_API_KEY'))
        if not self.api_key:
            logging.warning("Stable Diffusion API key not found in config or environment variables")

        self.api_url = "https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image"
        logging.info("Initialized Stable Diffusion image generator")

    def _init_craiyon(self):
        """Initialize Craiyon API"""
        self.api_url = "https://api.craiyon.com/v3"
        logging.info("Initialized Craiyon image generator")

    def _init_piapi_midjourney(self):
        """Initialize PiAPI Midjourney API"""
        self.api_key = self.config['api_keys'].get('piapi', os.environ.get('PIAPI_API_KEY'))
        if not self.api_key:
            logging.warning("PiAPI API key not found in config or environment variables")

        self.api_url = "https://api.piapi.ai/api/v1/task"
        logging.info("Initialized PiAPI Midjourney image generator")

    def generate_image(self, prompt: str, niche: str, output_filename: Optional[str] = None) -> str:
        """Generate an image based on prompt and niche

        Args:
            prompt: Text prompt for image generation
            niche: Content niche for context
            output_filename: Optional filename for the output image file

        Returns:
            str: Path to the generated image file
        """
        logging.info(f"Generating image for prompt: {prompt[:50]}...")

        # Enhance prompt based on niche
        enhanced_prompt = self._enhance_prompt(prompt, niche)

        # Generate filename if not provided
        if not output_filename:
            timestamp = int(time.time())
            output_filename = f"image_{timestamp}.png"

        output_path = os.path.join(self.output_dir, output_filename)

        try:
            # Generate image using selected provider
            if self.provider == 'stable_diffusion':
                image = self._generate_with_stable_diffusion(enhanced_prompt)
            elif self.provider == 'craiyon':
                image = self._generate_with_craiyon(enhanced_prompt)
            elif self.provider == 'piapi_midjourney':
                image = self._generate_with_piapi_midjourney(enhanced_prompt)
            else:
                raise ValueError(f"Unsupported image generation provider: {self.provider}")

            # Post-process and save image
            processed_image = self._post_process_image(image)
            processed_image.save(output_path)

            logging.info(f"Generated image saved to: {output_path}")
            return output_path
        except Exception as e:
            logging.error(f"Error generating image: {str(e)}")
            # Return a placeholder image if generation fails
            return self._create_placeholder_image(prompt, output_path)

    def _enhance_prompt(self, prompt: str, niche: str) -> str:
        """Enhance the prompt based on niche and best practices

        Args:
            prompt: Original prompt
            niche: Content niche

        Returns:
            str: Enhanced prompt
        """
        # Create scenic background prompts based on niche
        scenic_backgrounds = {
            "horror": "dark mysterious forest with fog, ancient trees, moonlight filtering through branches, eerie atmosphere",
            "scary": "haunted landscape, misty mountains, dark clouds, dramatic lighting, gothic scenery",
            "fantasy": "magical forest with glowing lights, enchanted trees, mystical atmosphere, fairy tale landscape",
            "adventure": "epic mountain landscape, flowing rivers, dramatic sky, cinematic nature view",
            "mystery": "foggy forest path, mysterious shadows, moonlit scenery, atmospheric landscape",
            "romance": "beautiful sunset landscape, peaceful lake, romantic scenery, warm golden light",
            "sci-fi": "futuristic landscape, alien world, dramatic sky, otherworldly scenery",
            "nature": "pristine wilderness, flowing river, majestic mountains, natural beauty",
            "default": "beautiful natural landscape, flowing river through forest, dramatic sky, cinematic view"
        }

        # Get appropriate background for niche
        background = scenic_backgrounds.get(niche.lower(), scenic_backgrounds["default"])

        # If prompt mentions specific scenery, use it, otherwise use niche-based background
        if any(word in prompt.lower() for word in ["forest", "mountain", "river", "lake", "ocean", "desert", "city"]):
            enhanced_prompt = f"{prompt}, cinematic view, high quality, detailed, atmospheric lighting"
        else:
            enhanced_prompt = f"{background}, cinematic view, high quality, detailed, atmospheric lighting, vertical composition"

        return enhanced_prompt

    def _generate_with_stable_diffusion(self, prompt: str) -> Image.Image:
        """Generate image using Stable Diffusion API

        Args:
            prompt: Enhanced prompt for image generation

        Returns:
            PIL.Image: Generated image
        """
        if not self.api_key:
            logging.warning("No Stable Diffusion API key available, using placeholder")
            return self._create_blank_image()

        try:
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            payload = {
                "text_prompts": [{"text": prompt}],
                "cfg_scale": 7,
                "height": self.height,
                "width": self.width,
                "samples": 1,
                "steps": 30,
            }

            response = requests.post(self.api_url, headers=headers, json=payload)

            if response.status_code != 200:
                logging.error(f"Stable Diffusion API error: {response.text}")
                return self._create_blank_image()

            data = response.json()
            image_data = base64.b64decode(data["artifacts"][0]["base64"])
            image = Image.open(io.BytesIO(image_data))
            return image
        except Exception as e:
            logging.error(f"Error with Stable Diffusion API: {str(e)}")
            return self._create_blank_image()

    def _generate_with_craiyon(self, prompt: str) -> Image.Image:
        """Generate image using Craiyon API

        Args:
            prompt: Enhanced prompt for image generation

        Returns:
            PIL.Image: Generated image
        """
        try:
            # Use the free Craiyon API
            payload = {
                "prompt": prompt
            }

            # Try the public Craiyon API endpoint
            api_url = "https://api.craiyon.com/v3"
            response = requests.post(api_url, json=payload, timeout=60)

            if response.status_code == 200:
                data = response.json()
                if "images" in data and len(data["images"]) > 0:
                    # Craiyon returns base64 encoded images
                    image_data = base64.b64decode(data["images"][0])
                    image = Image.open(io.BytesIO(image_data))

                    # Resize to target dimensions
                    image = image.resize((self.width, self.height), Image.LANCZOS)
                    return image

            # If Craiyon fails, create a beautiful placeholder
            logging.warning(f"Craiyon API failed, creating scenic placeholder for: {prompt}")
            return self._create_scenic_placeholder(prompt)

        except Exception as e:
            logging.error(f"Error with Craiyon API: {str(e)}")
            return self._create_scenic_placeholder(prompt)

    def _generate_with_piapi_midjourney(self, prompt: str) -> Image.Image:
        """Generate image using PiAPI Midjourney API

        Args:
            prompt: Enhanced prompt for image generation

        Returns:
            PIL.Image: Generated image
        """
        if not self.api_key:
            logging.warning("No PiAPI API key available, using placeholder")
            return self._create_blank_image()

        try:
            # Submit imagine task
            headers = {
                "Accept": "application/json",
                "x-api-key": self.api_key
            }

            payload = {
                "model": "midjourney",
                "task_type": "imagine",
                "input": {
                    "prompt": f"anime style {prompt}, cinematic lighting, detailed artwork --ar 9:16 --v 6",
                    "aspect_ratio": "9:16",
                    "process_mode": "fast",
                    "skip_prompt_check": True
                }
            }

            response = requests.post(self.api_url, headers=headers, json=payload)

            if response.status_code != 200:
                logging.error(f"PiAPI API error: {response.text}")
                return self._create_blank_image()

            result = response.json()
            task_id = result.get('data', {}).get('task_id')

            if not task_id:
                logging.error("Failed to get task ID from PiAPI")
                return self._create_blank_image()

            # Poll for completion
            image_url = self._wait_for_piapi_completion(task_id)

            if not image_url:
                logging.error("Failed to get image URL from PiAPI")
                return self._create_blank_image()

            # Download the image
            image_response = requests.get(image_url)
            image_response.raise_for_status()

            image = Image.open(io.BytesIO(image_response.content))

            # Resize to target dimensions if needed
            if image.width != self.width or image.height != self.height:
                image = image.resize((self.width, self.height), Image.LANCZOS)

            return image

        except Exception as e:
            logging.error(f"Error with PiAPI Midjourney API: {str(e)}")
            return self._create_blank_image()

    def _wait_for_piapi_completion(self, task_id: str, max_wait_time: int = 300) -> Optional[str]:
        """Wait for PiAPI task to complete and return image URL

        Args:
            task_id: Task ID to monitor
            max_wait_time: Maximum time to wait in seconds

        Returns:
            Optional[str]: Image URL if successful, None otherwise
        """
        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            try:
                headers = {
                    "Accept": "application/json",
                    "x-api-key": self.api_key
                }

                response = requests.get(f"{self.api_url}/{task_id}", headers=headers)
                response.raise_for_status()

                result = response.json()

                # Handle both direct response and nested data structure
                if 'data' in result:
                    data = result['data']
                else:
                    data = result

                status = data.get('status', '').lower()

                logging.debug(f"Task {task_id} status: {status}")

                if status == 'completed':
                    output = data.get('output', {})
                    image_url = output.get('image_url')
                    if image_url:
                        return image_url
                    # Try alternative URL formats
                    temp_urls = output.get('temporary_image_urls', [])
                    if temp_urls:
                        return temp_urls[0]
                elif status == 'failed':
                    error_msg = data.get('error', {}).get('message', 'Task failed')
                    logging.error(f"PiAPI task failed: {error_msg}")
                    return None

                # Wait before next check
                time.sleep(10)

            except Exception as e:
                logging.warning(f"Error checking PiAPI task status: {str(e)}")
                time.sleep(10)

        logging.error(f"PiAPI task {task_id} timed out after {max_wait_time} seconds")
        return None

    def _create_blank_image(self) -> Image.Image:
        """Create a blank image with the configured dimensions

        Returns:
            PIL.Image: Blank image
        """
        return Image.new('RGB', (self.width, self.height), color='black')

    def _create_scenic_placeholder(self, prompt: str) -> Image.Image:
        """Create a beautiful scenic placeholder image

        Args:
            prompt: The prompt to base the scenic image on

        Returns:
            PIL.Image: Scenic placeholder image
        """
        try:
            from PIL import ImageDraw, ImageFont
            import random

            # Create a gradient background based on prompt/niche
            if any(word in prompt.lower() for word in ["dark", "horror", "scary", "mysterious"]):
                # Dark theme
                colors = [(25, 25, 50), (50, 25, 75), (75, 50, 100)]
            elif any(word in prompt.lower() for word in ["sunset", "romance", "warm"]):
                # Warm theme
                colors = [(255, 150, 100), (255, 100, 150), (200, 100, 200)]
            elif any(word in prompt.lower() for word in ["forest", "nature", "green"]):
                # Nature theme
                colors = [(50, 100, 50), (75, 125, 75), (100, 150, 100)]
            else:
                # Default blue theme
                colors = [(100, 150, 200), (150, 200, 255), (200, 225, 255)]

            # Create gradient image
            image = Image.new('RGB', (self.width, self.height))
            draw = ImageDraw.Draw(image)

            # Create vertical gradient
            for y in range(self.height):
                ratio = y / self.height
                if ratio < 0.5:
                    # Top half
                    r = int(colors[0][0] + (colors[1][0] - colors[0][0]) * (ratio * 2))
                    g = int(colors[0][1] + (colors[1][1] - colors[0][1]) * (ratio * 2))
                    b = int(colors[0][2] + (colors[1][2] - colors[0][2]) * (ratio * 2))
                else:
                    # Bottom half
                    ratio = (ratio - 0.5) * 2
                    r = int(colors[1][0] + (colors[2][0] - colors[1][0]) * ratio)
                    g = int(colors[1][1] + (colors[2][1] - colors[1][1]) * ratio)
                    b = int(colors[1][2] + (colors[2][2] - colors[1][2]) * ratio)

                draw.line([(0, y), (self.width, y)], fill=(r, g, b))

            # Add some simple shapes to simulate landscape
            # Mountains/hills
            mountain_points = []
            for x in range(0, self.width, 50):
                y = self.height // 2 + random.randint(-100, 100)
                mountain_points.append((x, y))
            mountain_points.append((self.width, self.height))
            mountain_points.append((0, self.height))

            # Draw mountains with darker color
            mountain_color = (max(0, colors[1][0] - 50), max(0, colors[1][1] - 50), max(0, colors[1][2] - 50))
            draw.polygon(mountain_points, fill=mountain_color)

            # Add some "trees" (simple triangles)
            for i in range(5):
                x = random.randint(50, self.width - 50)
                y = random.randint(self.height // 2, self.height - 100)
                tree_color = (max(0, colors[0][0] - 30), max(0, colors[0][1] - 30), max(0, colors[0][2] - 30))
                draw.polygon([(x, y), (x - 20, y + 40), (x + 20, y + 40)], fill=tree_color)

            return image

        except Exception as e:
            logging.error(f"Error creating scenic placeholder: {str(e)}")
            return self._create_blank_image()

    def _create_placeholder_image(self, prompt: str, output_path: str) -> str:
        """Create a placeholder image with text

        Args:
            prompt: Text prompt
            output_path: Path to save the image

        Returns:
            str: Path to the placeholder image
        """
        try:
            from PIL import ImageDraw, ImageFont

            # Create a gradient background
            image = self._create_gradient_background()

            # Add text
            draw = ImageDraw.Draw(image)

            # Try to load a font, use default if not available
            try:
                font = ImageFont.truetype("arial.ttf", 40)
            except IOError:
                font = ImageFont.load_default()

            # Wrap text to fit image width
            wrapped_text = self._wrap_text(prompt, font, self.width - 100)

            # Calculate text position (centered)
            text_width, text_height = draw.multiline_textsize(wrapped_text, font=font) if hasattr(draw, 'multiline_textsize') else draw.textbbox((0, 0), wrapped_text, font=font)[2:4]
            position = ((self.width - text_width) // 2, (self.height - text_height) // 2)

            # Draw text with shadow for better visibility
            draw.multiline_text((position[0]+2, position[1]+2), wrapped_text, font=font, fill="black", align="center")
            draw.multiline_text(position, wrapped_text, font=font, fill="white", align="center")

            # Save image
            image.save(output_path)
            logging.info(f"Created placeholder image at: {output_path}")
            return output_path
        except Exception as e:
            logging.error(f"Error creating placeholder image: {str(e)}")
            # Create and save a simple blank image as last resort
            blank = self._create_blank_image()
            blank.save(output_path)
            return output_path

    def _create_gradient_background(self) -> Image.Image:
        """Create a gradient background image

        Returns:
            PIL.Image: Gradient background image
        """
        # Create a blank image
        image = Image.new('RGB', (self.width, self.height), color='black')

        # Create a simple gradient
        for y in range(self.height):
            r = int(255 * (1 - y / self.height))
            g = int(100 * (1 - y / self.height))
            b = int(150 * (1 - y / self.height))
            for x in range(self.width):
                image.putpixel((x, y), (r, g, b))

        return image

    def _wrap_text(self, text: str, font, max_width: int) -> str:
        """Wrap text to fit within max_width

        Args:
            text: Text to wrap
            font: Font to use for measuring text width
            max_width: Maximum width in pixels

        Returns:
            str: Wrapped text with newlines
        """
        words = text.split()
        wrapped_lines = []
        current_line = []

        for word in words:
            # Add word to current line
            current_line.append(word)

            # Check if current line is too wide
            line = ' '.join(current_line)
            line_width = font.getsize(line)[0] if hasattr(font, 'getsize') else font.getbbox(line)[2]

            if line_width > max_width:
                # Remove last word and add line to wrapped lines
                if len(current_line) > 1:
                    current_line.pop()
                    wrapped_lines.append(' '.join(current_line))
                    current_line = [word]
                else:
                    # If a single word is too wide, keep it and move to next line
                    wrapped_lines.append(line)
                    current_line = []

        # Add the last line
        if current_line:
            wrapped_lines.append(' '.join(current_line))

        return '\n'.join(wrapped_lines)

    def _post_process_image(self, image: Image.Image) -> Image.Image:
        """Apply post-processing effects to the image

        Args:
            image: Original generated image

        Returns:
            PIL.Image: Processed image
        """
        # Ensure correct dimensions
        if image.width != self.width or image.height != self.height:
            image = ImageOps.fit(image, (self.width, self.height), method=Image.LANCZOS)

        # Apply subtle enhancements
        image = ImageEnhance.Contrast(image).enhance(1.2)
        image = ImageEnhance.Brightness(image).enhance(1.1)
        image = ImageEnhance.Color(image).enhance(1.2)

        # Apply subtle sharpening
        image = image.filter(ImageFilter.SHARPEN)

        return image
