{"name": "TikTok Automation - Scary Stories Template", "nodes": [{"parameters": {"content": "# TikTok Automation Workflow\n\n## APIs Used:\n- OpenAI: Story generation\n- ApyHub: Text-to-speech\n- PiAPI: Midjourney image generation\n- Hedra: Video assembly\n- ImgBB: Image hosting\n- Google Drive: Final upload\n\n## Workflow:\n1. Generate scary story (OpenAI)\n2. Convert text to speech (ApyHub)\n3. Generate base image (PiAPI)\n4. Create enhanced image (PiAPI)\n5. Assemble video (Hedra)\n6. Upload to Google Drive", "height": 680, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1480, 1440], "id": "workflow-info", "name": "Workflow Info"}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-1380, 3880], "id": "schedule-trigger", "name": "Schedule Trigger"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4", "mode": "list", "cachedResultName": "GPT-4"}, "messages": {"values": [{"content": "You are an expert storyteller specializing in creating viral scary stories for TikTok. Your stories should be:\n- Exactly 60 seconds when read aloud\n- Engaging and suspenseful\n- Perfect for voice narration\n- Include natural pauses and dramatic timing\n- End with a chilling twist\n\nCreate a scary story that will captivate TikTok audiences. Focus on psychological horror, urban legends, or supernatural encounters. Make it vivid and atmospheric.", "role": "system"}, {"content": "Generate a viral scary story for TikTok that is exactly 60 seconds when read aloud. The story should be atmospheric, suspenseful, and end with a chilling twist. Focus on themes like:\n- Abandoned places\n- Mysterious whispers\n- Unexplained phenomena\n- Dark forests\n- Haunted locations\n\nMake it engaging for a young audience and perfect for voice narration.", "role": "user"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-1160, 3880], "id": "openai-story-generator", "name": "OpenAI - Generate Scary Story", "credentials": {"openAiApi": {"id": "openai-credentials", "name": "OpenAI API"}}}, {"parameters": {"method": "POST", "url": "https://api.apyhub.com/generate/text/audio/file", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apy-token", "value": "APY0jyr56nnuCm5cazBjuYIVTOn1FyiFZAitdVzBxtGoVCek73LHlyynZk9ZZ2ll5DAeh6XNf9Mwj"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"text\": \"{{ $json.message.content }}\",\n  \"gender\": \"female\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-940, 3880], "id": "apyhub-tts", "name": "ApyHub - Text to Speech"}, {"parameters": {"amount": 2, "unit": "minutes"}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [-720, 3880], "id": "wait-audio", "name": "Wait for Audio", "webhookId": "audio-wait-webhook"}, {"parameters": {"method": "POST", "url": "https://api.piapi.ai/api/v1/task", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "x-api-key", "value": "6d9c3e22532462701f580ce41a4e03100037876de129c4d8d359c6c18da3996c"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"model\": \"midjourney\",\n  \"task_type\": \"imagine\",\n  \"input\": {\n    \"prompt\": \"dark atmospheric forest scene with mysterious shadows and eerie lighting, perfect for scary story content, tall trees silhouetted against dim sky with fog, cinematic lighting, detailed artwork --ar 9:16 --v 6\",\n    \"aspect_ratio\": \"9:16\",\n    \"process_mode\": \"fast\",\n    \"skip_prompt_check\": true\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-500, 3880], "id": "piapi-base-image", "name": "PiAPI - Generate Base Image"}, {"parameters": {"amount": 3, "unit": "minutes"}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [-280, 3880], "id": "wait-image", "name": "Wait for Image", "webhookId": "image-wait-webhook"}, {"parameters": {"method": "POST", "url": "https://api.imgbb.com/1/upload", "sendBody": true, "specifyBody": "form", "bodyParameters": {"parameters": [{"name": "key", "value": "c9a3ba8c4f8c8f8e8e8e8e8e8e8e8e8e"}, {"name": "image", "value": "={{ $json.task_result.task_result.image_url }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-60, 3880], "id": "imgbb-upload", "name": "ImgBB - Upload Image"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4", "mode": "list", "cachedResultName": "GPT-4"}, "messages": {"values": [{"content": "Transform this image description into a detailed visual prompt for scary story animation. Focus on:\n- Atmospheric movements and animations\n- Eerie lighting effects\n- Mysterious character expressions\n- Haunting environmental details\n- Cinematic camera movements\n\nMake it perfect for animated video generation with Hedra AI.", "role": "system"}, {"content": "=Transform this image into an animated scary story visual: {{ $json.data.display_url }}", "role": "user"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [160, 3880], "id": "openai-visual-prompt", "name": "OpenAI - Create Visual Prompt", "credentials": {"openAiApi": {"id": "openai-credentials", "name": "OpenAI API"}}}, {"parameters": {"method": "POST", "url": "https://api.hedra.com/v1/projects", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer hd_sk_b8f7e6d5c4a3b2e1f9g8h7i6j5k4l3m2n1o0p9q8r7s6t5u4v3w2x1y0z"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"aspectRatio\": \"9:16\",\n  \"audioSource\": \"{{ $('ApyHub - Text to Speech').item.json.data }}\",\n  \"videoInputs\": [\n    {\n      \"filename\": \"scary_story.jpg\",\n      \"url\": \"{{ $('ImgBB - Upload Image').item.json.data.display_url }}\"\n    }\n  ],\n  \"text\": \"{{ $('OpenAI - Create Visual Prompt').item.json.message.content }}\",\n  \"duration\": 10000\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [380, 3880], "id": "hedra-video", "name": "Hedra - Generate Video (10s Optimized)"}, {"parameters": {"amount": 5, "unit": "minutes"}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [600, 3880], "id": "wait-video", "name": "Wait for Video Generation", "webhookId": "video-wait-webhook"}, {"parameters": {"resource": "file", "operation": "upload", "name": "=TikTok_Scary_Story_{{ new Date().toISOString().slice(0,10) }}.mp4", "resolveData": true, "parents": {"__rl": true, "value": "1BQf8vqJ_vM8XoKzQzQzQzQzQzQzQzQz", "mode": "list"}, "binaryData": true, "fileContent": "={{ $json.videoUrl }}", "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [820, 3880], "id": "google-drive-upload", "name": "Google Drive - Upload Video", "credentials": {"googleDriveOAuth2Api": {"id": "google-drive-credentials", "name": "Google Drive API"}}}], "pinData": {}, "connections": {"Schedule Trigger": {"main": [[{"node": "OpenAI - Generate Scary Story", "type": "main", "index": 0}]]}, "OpenAI - Generate Scary Story": {"main": [[{"node": "ApyHub - Text to Speech", "type": "main", "index": 0}]]}, "ApyHub - Text to Speech": {"main": [[{"node": "Wait for Audio", "type": "main", "index": 0}]]}, "Wait for Audio": {"main": [[{"node": "PiAPI - Generate Base Image", "type": "main", "index": 0}]]}, "PiAPI - Generate Base Image": {"main": [[{"node": "Wait for Image", "type": "main", "index": 0}]]}, "Wait for Image": {"main": [[{"node": "ImgBB - Upload Image", "type": "main", "index": 0}]]}, "ImgBB - Upload Image": {"main": [[{"node": "OpenAI - Create Visual Prompt", "type": "main", "index": 0}]]}, "OpenAI - Create Visual Prompt": {"main": [[{"node": "Hedra - Generate Video (10s Optimized)", "type": "main", "index": 0}]]}, "Hedra - Generate Video (10s Optimized)": {"main": [[{"node": "Wait for Video Generation", "type": "main", "index": 0}]]}, "Wait for Video Generation": {"main": [[{"node": "Google Drive - Upload Video", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "meta": {"templateCredsSetupCompleted": false}, "tags": [{"name": "tiktok-automation"}]}