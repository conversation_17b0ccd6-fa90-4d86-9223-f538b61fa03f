#!/usr/bin/env python3
"""
Quick Test for TikTok Automation - Real APIs
"""

import json
import sys
import os

def test_imports():
    """Test if all imports work"""
    print("🧪 Testing ALL imports...")
    print("=" * 40)
    
    imports_to_test = [
        ("gtts", "gTTS"),
        ("moviepy.editor", "MoviePy"),
        ("pyttsx3", "pyttsx3"),
        ("httpx", "httpx"),
        ("aiofiles", "aiofiles"),
        ("PIL", "Pillow"),
        ("cv2", "OpenCV"),
        ("requests", "requests"),
        ("numpy", "numpy")
    ]
    
    all_good = True
    for module, name in imports_to_test:
        try:
            __import__(module)
            print(f"   ✅ {name}")
        except ImportError as e:
            print(f"   ❌ {name} - {e}")
            all_good = False
    
    return all_good

def verify_config():
    """Verify configuration"""
    print("\n🔍 Verifying configuration...")
    
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        # Check providers
        tts_provider = config.get('tts', {}).get('provider')
        image_provider = config.get('image', {}).get('provider')
        
        print(f"   TTS Provider: {tts_provider}")
        print(f"   Image Provider: {image_provider}")
        
        # Check API keys
        api_keys = config.get('api_keys', {})
        required_keys = ['openai', 'apyhub', 'piapi', 'hedra']
        
        keys_ok = True
        for key in required_keys:
            if api_keys.get(key):
                print(f"   ✅ {key.upper()} API key configured")
            else:
                print(f"   ❌ {key.upper()} API key missing")
                keys_ok = False
        
        # Check if enhanced workflow is enabled
        ai_workflow = config.get('ai_workflow', {})
        apyhub_enabled = ai_workflow.get('apyhub_tts', {}).get('enabled', False)
        piapi_enabled = ai_workflow.get('piapi_midjourney', {}).get('generation_enabled', False)
        
        print(f"   ApyHub TTS: {'✅ Enabled' if apyhub_enabled else '❌ Disabled'}")
        print(f"   PiAPI Midjourney: {'✅ Enabled' if piapi_enabled else '❌ Disabled'}")
        
        if (tts_provider == 'apyhub' and image_provider == 'piapi_midjourney' and 
            apyhub_enabled and piapi_enabled and keys_ok):
            print("\n✅ Configuration is PERFECT for real API usage!")
            return True
        else:
            print("\n⚠️ Configuration needs adjustment")
            return False
            
    except Exception as e:
        print(f"❌ Error checking config: {e}")
        return False

def test_basic_functionality():
    """Test basic app functionality"""
    print("\n🔧 Testing basic functionality...")
    
    # Add current directory to Python path
    sys.path.insert(0, os.getcwd())
    
    try:
        # Test story generator
        print("   Testing story generator...")
        from src.story_generation.story_generator import StoryGenerator
        print("   ✅ Story generator import OK")
        
        # Test speech generator
        print("   Testing speech generator...")
        from src.tts.speech_generator import SpeechGenerator
        print("   ✅ Speech generator import OK")
        
        # Test image generator
        print("   Testing image generator...")
        from src.image_generation.image_generator import ImageGenerator
        print("   ✅ Image generator import OK")
        
        # Test video generator
        print("   Testing video generator...")
        from src.video_generation.video_generator import VideoGenerator
        print("   ✅ Video generator import OK")
        
        # Test enhanced pipeline
        print("   Testing enhanced pipeline...")
        from src.core.pipeline import VideoGenerationPipeline
        print("   ✅ Enhanced pipeline import OK")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def main():
    """Main function"""
    print("🎬 TikTok Automation - QUICK TEST")
    print("=" * 50)
    
    # Test imports
    imports_ok = test_imports()
    
    # Verify config
    config_ok = verify_config()
    
    # Test functionality
    func_ok = test_basic_functionality()
    
    print("\n" + "=" * 50)
    print("🎯 QUICK TEST SUMMARY:")
    print(f"   Dependencies: {'✅ OK' if imports_ok else '❌ FAILED'}")
    print(f"   Configuration: {'✅ OK' if config_ok else '❌ FAILED'}")
    print(f"   Functionality: {'✅ OK' if func_ok else '❌ FAILED'}")
    
    if imports_ok and config_ok and func_ok:
        print("\n🎉 EVERYTHING IS READY!")
        print("\n🚀 Ready to test with REAL APIs:")
        print("   • ApyHub for human-like audio")
        print("   • PiAPI for Midjourney images")
        print("   • Hedra for video assembly")
        print("   • OpenAI for story generation")
        
        print("\n📋 Test Data:")
        print("   Account: test_real_apis")
        print("   Niche: horror")
        print("   Theme: Scary Stories")
        print("   Topic: A mirror that shows your future")
        print("   Voice: female")
        
        print("\n🎬 Expected Result:")
        print("   • High-quality human voice (ApyHub)")
        print("   • Beautiful Midjourney image (PiAPI)")
        print("   • Professional video (Hedra)")
        print("   • All APIs will be consumed!")
        
        print("\n▶️ RUN: python main.py")
        
    else:
        print("\n❌ ISSUES FOUND:")
        if not imports_ok:
            print("   - Some dependencies missing")
        if not config_ok:
            print("   - Configuration problems")
        if not func_ok:
            print("   - Functionality issues")
        print("\n🔧 Please fix the issues above")
    
    input("\nPress Enter to continue...")

if __name__ == "__main__":
    main()
