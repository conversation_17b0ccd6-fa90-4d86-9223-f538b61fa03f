"""
Hedra Video Assembler for TikTok Automation
Animated video generation using Hedra API
"""

import os
import logging
import time
import asyncio
import shutil
from typing import Dict, Any, Optional, List
import json
from ..core.debug_config import debug_config

try:
    import httpx
except ImportError:
    logging.warning("httpx package not installed. Hedra video assembler will not be available.")

class HedraVideoAssembler:
    """Hedra API integration for video assembly"""

    def __init__(self, config: Dict[str, Any]):
        """Initialize Hedra video assembler

        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.api_key = config['api_keys']['hedra']
        self.enabled = config['ai_workflow']['hedra_video']['enabled']
        self.quality = config['ai_workflow']['hedra_video']['quality']
        self.aspect_ratio = config['ai_workflow']['hedra_video']['aspect_ratio']

        # API endpoints
        self.base_url = "https://api.hedra.com/web-app/public"
        self.assets_endpoint = f"{self.base_url}/assets"
        self.generate_endpoint = f"{self.base_url}/generations"

        # Initialize HTTP client
        self.client = httpx.AsyncClient(
            timeout=300.0,  # 5 minutes for video generation
            headers={
                "X-API-Key": self.api_key
            }
        )

        # Output directory
        self.output_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "data", "videos")
        os.makedirs(self.output_dir, exist_ok=True)

    async def generate_video(self, image_path: str, audio_path: str,
                           output_filename: Optional[str] = None,
                           animation_style: str = "natural") -> Dict[str, Any]:
        """Generate animated video using Hedra API

        Args:
            image_path: Path to the source image
            audio_path: Path to the audio file
            output_filename: Optional filename for output
            animation_style: Animation style (natural, dramatic, subtle)

        Returns:
            Dict containing video generation result
        """
        # Check if debug mode is enabled for video
        if debug_config.should_use_debug_video():
            logging.info(f"DEBUG MODE: Creating mock video with '{animation_style}' animation instead of using Hedra API")

            # Generate filename if not provided
            if not output_filename:
                timestamp = int(time.time())
                output_filename = f"video_{timestamp}.mp4"

            output_path = os.path.join(self.output_dir, output_filename)

            # Create animation prompt for debug logging
            animation_prompts = {
                "natural": "Natural subtle movements, gentle breathing, soft eye blinks",
                "dramatic": "Intense dramatic expressions, dynamic head movements, strong emotional reactions",
                "subtle": "Very subtle micro-expressions, gentle breathing, minimal movement",
                "energetic": "Animated expressions, dynamic movements, engaging gestures",
                "calm": "Peaceful serene expressions, slow gentle movements, tranquil breathing",
                "scary": "Mysterious atmospheric movements, subtle tension in facial expressions, eerie ambient lighting, haunting gaze with slow deliberate movements"
            }

            animation_prompt = animation_prompts.get(animation_style, animation_prompts["scary"])
            logging.info(f"DEBUG: Animation prompt would be: {animation_prompt}")

            # Create a simple mock video by creating a static video from the image
            try:
                # Import MoviePy for creating a simple video
                try:
                    from moviepy.editor import ImageClip, AudioFileClip

                    # Create a simple static video from the image and audio
                    image_clip = ImageClip(image_path, duration=None)
                    audio_clip = AudioFileClip(audio_path)

                    # Set video duration to match audio
                    video_clip = image_clip.set_duration(audio_clip.duration)

                    # Combine video and audio
                    final_clip = video_clip.set_audio(audio_clip)

                    # Write the video file
                    final_clip.write_videofile(
                        output_path,
                        fps=24,
                        codec='libx264',
                        audio_codec='aac',
                        verbose=False,
                        logger=None
                    )

                    # Clean up
                    final_clip.close()
                    audio_clip.close()
                    image_clip.close()

                    logging.info(f"DEBUG: Mock video created successfully: {output_path}")

                except ImportError:
                    # If MoviePy is not available, create a dummy file
                    with open(output_path, 'wb') as f:
                        f.write(b'MOCK_VIDEO_FILE')
                    logging.info(f"DEBUG: Mock video placeholder created: {output_path}")

                result = {
                    'path': output_path,
                    'generation_id': 'debug_generation',
                    'animation_style': animation_style,
                    'animation_prompt': animation_prompt,
                    'quality': self.quality,
                    'aspect_ratio': self.aspect_ratio,
                    'timestamp': time.time(),
                    'debug_mode': True
                }

                return result

            except Exception as e:
                logging.warning(f"⚠️ DEBUG: Mock video creation failed: {e}, falling back to API")

        if not self.enabled:
            raise ValueError("Hedra video generation is disabled in configuration")

        if not self.api_key:
            raise ValueError("Hedra API key not configured")

        if not os.path.exists(image_path):
            raise FileNotFoundError(f"Image file not found: {image_path}")

        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"Audio file not found: {audio_path}")

        logging.info(f"Generating video with Hedra: {image_path} + {audio_path}")

        # Generate filename if not provided
        if not output_filename:
            timestamp = int(time.time())
            output_filename = f"video_{timestamp}.mp4"

        output_path = os.path.join(self.output_dir, output_filename)

        try:
            # Upload files and start generation
            generation_id = await self._start_generation(image_path, audio_path, animation_style)

            # Poll for completion
            video_url = await self._wait_for_completion(generation_id)

            # Download the generated video
            await self._download_video(video_url, output_path)

            result = {
                'path': output_path,
                'generation_id': generation_id,
                'animation_style': animation_style,
                'quality': self.quality,
                'aspect_ratio': self.aspect_ratio,
                'timestamp': time.time()
            }

            logging.info(f"Video generated successfully: {output_path}")
            return result

        except Exception as e:
            logging.error(f"Error generating video with Hedra: {str(e)}")
            raise

    async def _start_generation(self, image_path: str, audio_path: str,
                              animation_style: str) -> str:
        """Start video generation process

        Args:
            image_path: Path to source image
            audio_path: Path to audio file
            animation_style: Animation style

        Returns:
            str: Generation ID
        """
        try:
            # Read files as base64 for JSON payload
            import base64

            with open(image_path, 'rb') as f:
                image_data = base64.b64encode(f.read()).decode('utf-8')

            with open(audio_path, 'rb') as f:
                audio_data = base64.b64encode(f.read()).decode('utf-8')

            # Create animation prompt based on style
            animation_prompts = {
                "natural": "Natural subtle movements, gentle breathing, soft eye blinks, minimal head movement with calm expression",
                "dramatic": "Intense dramatic expressions, dynamic head movements, strong emotional reactions, cinematic lighting effects",
                "subtle": "Very subtle micro-expressions, gentle breathing, minimal movement with focused attention",
                "energetic": "Animated expressions, dynamic movements, engaging gestures, lively facial expressions",
                "calm": "Peaceful serene expressions, slow gentle movements, tranquil breathing, meditative focus",
                "scary": "Mysterious atmospheric movements, subtle tension in facial expressions, eerie ambient lighting, haunting gaze with slow deliberate movements"
            }

            # Get animation prompt based on style, default to scary for horror content
            animation_prompt = animation_prompts.get(animation_style, animation_prompts["scary"])

            # Create JSON payload with required type field and animation prompt
            payload = {
                "type": "video",
                "ai_model_id": "d1dd37a3-e39a-4854-a298-6510289f9cf2",  # Hedra AI model ID
                "image": f"data:image/jpeg;base64,{image_data}",
                "audio": f"data:audio/mpeg;base64,{audio_data}",
                "generated_video_inputs": {
                    "text_prompt": animation_prompt,
                    "resolution": "720p",
                    "aspect_ratio": self.aspect_ratio,
                    "duration_ms": 30000  # 30 seconds duration
                }
            }

            response = await self.client.post(
                self.generate_endpoint,
                json=payload,
                headers={
                    "X-API-Key": self.api_key,
                    "Content-Type": "application/json"
                }
            )

            # Log response details for debugging
            logging.info(f"Hedra API response status: {response.status_code}")
            if response.status_code != 200:
                logging.error(f"Hedra API error response: {response.text}")

            response.raise_for_status()

            # Handle response
            result = response.json()
            generation_id = result.get("id") or result.get("generation_id") or result.get("jobId")

            if generation_id:
                logging.info(f"Hedra generation started: {generation_id}")
                return generation_id
            else:
                logging.error(f"No generation ID in response: {result}")
                raise Exception(f"No generation ID in response: {result}")

        except Exception as e:
            logging.error(f"Error starting Hedra generation: {str(e)}")
            raise

    async def _upload_asset(self, file_path: str, asset_type: str) -> str:
        """Upload asset to Hedra and return asset ID

        Args:
            file_path: Path to the file
            asset_type: Type of asset (image or audio)

        Returns:
            str: Asset ID
        """
        try:
            # Upload file directly with multipart form data
            with open(file_path, 'rb') as f:
                # Determine content type
                if asset_type == "image":
                    content_type = "image/jpeg" if file_path.lower().endswith(('.jpg', '.jpeg')) else "image/png"
                    field_name = "image"
                else:  # audio
                    content_type = "audio/mpeg"
                    field_name = "audio"

                files = {
                    field_name: (os.path.basename(file_path), f, content_type)
                }

                # Create asset with file upload
                response = await self.client.post(
                    self.assets_endpoint,
                    files=files,
                    headers={
                        "X-API-Key": self.api_key
                        # Don't set Content-Type for multipart uploads
                    }
                )
                response.raise_for_status()

                result = response.json()
                asset_id = result.get("id")

                if not asset_id:
                    raise Exception(f"Failed to upload {asset_type} asset: {result}")

            logging.info(f"Uploaded {asset_type} asset: {asset_id}")
            return asset_id

        except Exception as e:
            logging.error(f"Error uploading {asset_type} asset: {str(e)}")
            raise

    async def _wait_for_completion(self, generation_id: str,
                                 max_wait_time: int = 300) -> str:
        """Wait for video generation to complete

        Args:
            generation_id: Generation ID to monitor
            max_wait_time: Maximum wait time in seconds

        Returns:
            str: URL of generated video
        """
        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            try:
                # Check generation status
                status_url = f"{self.generate_endpoint}/{generation_id}"
                response = await self.client.get(
                    status_url,
                    headers={"X-API-Key": self.api_key}
                )
                response.raise_for_status()

                # Parse JSON response
                result = response.json()
                status = result.get("status", "").lower()
                progress = result.get("progress", 0)

                logging.info(f"Generation {generation_id}: {status} ({progress}%)")

                if status == "completed" or status == "success":
                    video_url = (result.get("video_url") or
                               result.get("url") or
                               result.get("videoUrl") or
                               result.get("output", {}).get("video_url"))
                    if video_url:
                        return video_url
                elif status == "failed" or status == "error":
                    error_msg = result.get("error", {}).get("message", "Generation failed")
                    raise Exception(f"Hedra generation failed: {error_msg}")

                # Wait before next check
                await asyncio.sleep(10)

            except Exception as e:
                logging.error(f"Error checking generation status: {str(e)}")
                raise

        raise TimeoutError(f"Video generation timed out after {max_wait_time} seconds")

    async def _download_video(self, video_url: str, output_path: str) -> None:
        """Download generated video

        Args:
            video_url: URL of the generated video
            output_path: Local path to save the video
        """
        try:
            response = await self.client.get(video_url)
            response.raise_for_status()

            with open(output_path, 'wb') as f:
                f.write(response.content)

            logging.info(f"Video downloaded: {output_path}")

        except Exception as e:
            logging.error(f"Error downloading video: {str(e)}")
            raise

    async def get_generation_status(self, generation_id: str) -> Dict[str, Any]:
        """Get status of a video generation

        Args:
            generation_id: Generation ID to check

        Returns:
            Dict: Status information
        """
        try:
            response = await self.client.get(f"{self.status_endpoint}/{generation_id}")
            response.raise_for_status()

            result = response.json()

            if not result.get('success'):
                raise Exception(f"Status check failed: {result.get('error', 'Unknown error')}")

            return result['data']

        except Exception as e:
            logging.error(f"Error getting generation status: {str(e)}")
            raise

    async def cancel_generation(self, generation_id: str) -> bool:
        """Cancel a video generation

        Args:
            generation_id: Generation ID to cancel

        Returns:
            bool: True if successful
        """
        try:
            response = await self.client.delete(f"{self.generate_endpoint}/{generation_id}")
            response.raise_for_status()

            result = response.json()
            success = result.get('success', False)

            if success:
                logging.info(f"Generation cancelled: {generation_id}")

            return success

        except Exception as e:
            logging.warning(f"Error cancelling generation: {str(e)}")
            return False

    def get_supported_formats(self) -> List[str]:
        """Get supported video formats

        Returns:
            List: Supported formats
        """
        return ['mp4', 'mov', 'avi']

    def get_supported_aspect_ratios(self) -> List[str]:
        """Get supported aspect ratios

        Returns:
            List: Supported aspect ratios
        """
        return ['9:16', '16:9', '1:1', '4:5']

    def get_animation_styles(self) -> List[str]:
        """Get available animation styles

        Returns:
            List: Available animation styles
        """
        return ['natural', 'dramatic', 'subtle', 'energetic', 'calm']

    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()

    def __del__(self):
        """Cleanup on destruction"""
        try:
            asyncio.create_task(self.close())
        except:
            pass
