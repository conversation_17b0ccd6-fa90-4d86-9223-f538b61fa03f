#!/usr/bin/env python3
"""
Simple test script for ApyHub TTS API
"""

import os
import tempfile
import requests
import json

print("🚀 Simple ApyHub TTS API Test")
print("=" * 40)

# Load API key from config
try:
    with open('config.json', 'r') as f:
        config = json.load(f)
    API_TOKEN = config['api_keys']['apyhub']
    print(f"✅ Loaded API key: {API_TOKEN[:10]}...{API_TOKEN[-4:]}")
except Exception as e:
    print(f"❌ Failed to load config: {e}")
    exit(1)

API_URL = 'https://api.apyhub.com/tts/file'
print(f"📡 API URL: {API_URL}")

# Create a simple HTML file (supported format)
test_text = "Hello, this is a test message for ApyHub."
print(f"📝 Test text: {test_text}")

# Create HTML content
html_content = f"""<!DOCTYPE html>
<html>
<head><title>TTS Content</title></head>
<body>
<p>{test_text}</p>
</body>
</html>"""

# Create temp file with HTML format
with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as temp_file:
    temp_file.write(html_content)
    temp_file_path = temp_file.name

print(f"📁 Created temp file: {temp_file_path}")
print(f"📏 File size: {os.path.getsize(temp_file_path)} bytes")

try:
    # Read file content to verify
    with open(temp_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    print(f"📖 File content: '{content}'")

    # Prepare the request
    with open(temp_file_path, 'rb') as f:
        files = {
            'file': ('test.html', f, 'text/html')
        }
        data = {
            'gender': 'female'
        }
        headers = {
            'apy-token': API_TOKEN
        }

        print(f"🔧 Request headers: {headers}")
        print(f"🔧 Request data: {data}")
        print("📤 Sending request...")

        # Send the POST request
        response = requests.post(API_URL, headers=headers, files=files, data=data)

    print(f"📥 Response status: {response.status_code}")
    print(f"📥 Response headers: {dict(response.headers)}")

    if response.status_code == 200:
        output_path = 'test_output.mp3'
        with open(output_path, 'wb') as f:
            f.write(response.content)
        print(f"✅ SUCCESS! Audio file saved as '{output_path}'")
        print(f"🎵 Audio file size: {len(response.content)} bytes")
    else:
        print(f"❌ ERROR: {response.status_code}")
        print(f"📄 Response text: {response.text}")

        # Try to parse error JSON
        try:
            error_data = response.json()
            print(f"📄 Error details: {error_data}")
        except:
            print("📄 Could not parse error as JSON")

finally:
    # Clean up temp file
    if os.path.exists(temp_file_path):
        os.unlink(temp_file_path)
        print(f"🧹 Cleaned up temp file")

print("=" * 40)
print("🏁 Test completed")
