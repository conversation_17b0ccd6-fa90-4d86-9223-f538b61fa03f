#!/usr/bin/env python3
"""
Install ALL Required Dependencies for TikTok Automation
"""

import subprocess
import sys
import os

def install_dependencies():
    """Install all required dependencies"""
    
    print("📦 Installing ALL TikTok Automation Dependencies...")
    print("=" * 60)
    
    # List of ALL required packages
    packages = [
        "gtts>=2.3.0",           # Google Text-to-Speech
        "moviepy>=1.0.3",        # Video editing
        "pyttsx3>=2.90",         # Better TTS engine
        "pydub>=0.25.1",         # Audio conversion
        "httpx>=0.24.0",         # HTTP client for APIs
        "aiofiles>=23.0.0",      # Async file operations
        "requests>=2.28.0",      # HTTP requests
        "pillow>=9.0.0",         # Image processing
        "opencv-python>=4.7.0",  # Video processing
        "numpy>=1.21.0",         # Numerical operations
        "asyncio-mqtt>=0.11.0"   # Async MQTT
    ]
    
    failed_packages = []
    
    for package in packages:
        print(f"📦 Installing {package}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package], 
                                stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print(f"✅ Successfully installed {package}")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package}")
            failed_packages.append(package)
    
    if failed_packages:
        print(f"\n⚠️ Failed to install: {', '.join(failed_packages)}")
        print("Please install them manually:")
        for pkg in failed_packages:
            print(f"   pip install {pkg}")
        return False
    
    print("\n🎉 ALL dependencies installed successfully!")
    return True

def test_imports():
    """Test if all imports work"""
    print("\n🧪 Testing imports...")
    
    imports_to_test = [
        ("gtts", "gTTS"),
        ("moviepy.editor", "VideoFileClip"),
        ("pyttsx3", "pyttsx3"),
        ("httpx", "httpx"),
        ("aiofiles", "aiofiles"),
        ("PIL", "Image"),
        ("cv2", "OpenCV")
    ]
    
    for module, name in imports_to_test:
        try:
            __import__(module)
            print(f"   ✅ {name}")
        except ImportError:
            print(f"   ❌ {name} - install failed")

if __name__ == "__main__":
    print("🎬 TikTok Automation - Complete Dependency Installation")
    print("=" * 60)
    
    success = install_dependencies()
    test_imports()
    
    if success:
        print("\n✨ Ready to use ALL APIs!")
        print("• 🎙️ ApyHub for high-quality audio")
        print("• 🎨 PiAPI (Midjourney) for images")
        print("• 🎬 Hedra for video assembly")
        print("• 🔧 All fallback systems working")
        print("\n🚀 Run: python main.py")
    else:
        print("\n⚠️ Some dependencies failed. Please install manually.")
    
    input("\nPress Enter to continue...")
